"use strict";
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require("path");
const { version } = require("../package.json"); // 获取 package.json 中的版本号

const env =
  process.env.npm_lifecycle_event === "build"
    ? require("./prod.env")
    : require("./test.env");

let apiTarget;
if ((process.argv + "").indexOf("403") > -1) {
  // 403开发环境模拟登陆用户
  apiTarget = "http://***********:35693/";
  console.log("======403api:" + apiTarget);
} else {
  //  apiTarget = "http://localhost:8097/"; // 吕仁朋
  // apiTarget = "http://***************:8097/"; //康培忠
  //apiTarget = "http://***********:36845/"; // 政务云测试
  // apiTarget = "https://***********:34093/api/"; //403
  // apiTarget = "http://************:8097/"; // fgs 本地测试
  apiTarget = "https://***********:39094/api"; //  .青岛本地测试
  console.log("======other-api:" + apiTarget);
}
module.exports = {
  dev: {
    // Paths
    assetsSubDirectory: "static",
    assetsPublicPath: "/",
    proxyTable: {
      "/api": {
        target: apiTarget,
        secure: false, // 是否https接口
        changeOrigin: true, // 是否跨域
        pathRewrite: {
          "^/api": "",
        },
      },
      "/chat": {
        target: apiTarget,
        ws: true, // 如果需要代理 WebSocket，请确保开启
        onProxyReq: (proxyReq, req, res) => {
          if (
            req.headers.accept &&
            req.headers.accept.indexOf("text/event-stream") !== -1
          ) {
            proxyReq.setHeader("Connection", "keep-alive");
          }
        },
        onProxyRes: (proxyRes, req, res) => {
          if (
            req.headers.accept &&
            req.headers.accept.indexOf("text/event-stream") !== -1
          ) {
            proxyRes.headers["Connection"] = "keep-alive";
          }
        },
        secure: false, // 是否https接口
        changeOrigin: true, // 是否跨域
        pathRewrite: {
          "^/chat": "",
        },
      },
    },
    // Various Dev Server settings
    env: require("./dev.env"),
    host: "localhost",
    //  host: '*************', // can be overwritten by process.env.HOST
    port: 8080, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    /**
     * Source Maps
     */
    // https://webpack.js.org/configuration/devtool/#development
    devtool: "cheap-module-eval-source-map",

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: false,
    cssSourceMap: false,
  },
  build: {
    env: env,
    // Template for index.html
    index: path.resolve(__dirname, `../dist/${version}/index.html`),

    // Paths
    assetsRoot: path.resolve(__dirname, `../dist/${version}`),
    assetsSubDirectory: "static",
    assetsPublicPath: "./",

    /**
     * Source Maps
     */

    productionSourceMap: false, // true生成sourcemap便于调试，体积较大较为耗时
    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ["js", "css"],
    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report,
  },
};
