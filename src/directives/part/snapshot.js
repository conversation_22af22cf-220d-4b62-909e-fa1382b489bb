import html2canvas from "html2canvas";
const snapshot = {
  bind(vm, c) {
    // if (c.value && c.value != c.oldValue)
    //   console.log('bind:', vm, c)
  },
  inserted(vm, c) {
    // if(c.value&&c.value!=c.oldValue)
    // console.log('inserted:',vm,c)
  },
  update(vm, c, vnode) {
    let that = vnode.context;
    function downloadFile(fileName, content) {
      let aLink = document.createElement("a");
      let blob = base64ToBlob(content);

      let evt = document.createEvent("HTMLEvents");

      evt.initEvent("click", true, true);
      aLink.download = fileName;
      aLink.href = URL.createObjectURL(blob);
      aLink.dispatchEvent(
        new MouseEvent("click", {
          bubbles: true,
          cancelable: true,
          view: window,
        })
      );
    }

    function base64ToBlob(code) {
      let parts = code.split(";base64,");
      let contentType = parts[0].split(":")[1];
      let raw = window.atob(parts[1]);
      let rawLength = raw.length;

      let uInt8Array = new Uint8Array(rawLength);
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }
      return new Blob([uInt8Array], {
        type: contentType,
      });
    }
    console.log(c);
    if (
      c.value &&
      c.value.loadImage &&
      c.value.loadImage != c.oldValue.loadImage
    ) {
      if (that && that.$Message) {
        that.$Message.loading({
          // content: "图片生成中...",
          duration: 5,
          render: h => {
            return h('span',{
              style: {
                color: '#fff',
                backgroundColor: 'green',
                margin:'-8px -16px -8px -40px',
                padding:'8px 10px 8px 40px',
                display:'inline-block',
                borderRadius: '4px',
              }
            }, [
                '图片生成中...'])
        }
        });
      }
      // 设置放大倍数
      const scale = window.devicePixelRatio * 2;

      // 传入节点原始宽高
      const _width = vm.offsetWidth;
      const _height = vm.offsetHeight;

      let { width, height } = c.value.options;
      width = width || _width;
      height = height || _height;

      // html2canvas配置项
      const ops = {
        dpi: 350,
        scale,
        width,
        height,
        useCORS: true,
        allowTaint: false,
        ...c.value.options,
      };

      return html2canvas(vm, ops).then((canvas) => {
        // 返回图片的二进制数据
        let canvasImage = canvas.toDataURL("image/png");
        downloadFile("图片.png", canvasImage);
        return canvasImage;
      });
    }
  },
  componentUpdated(vm, c) {
    // if (c.value && c.value != c.oldValue) console.log('componentUpdated:', vm, c)
  },
  unbind(vm, c) {
    // if (c.value && c.value != c.oldValue) console.log('unbind:', vm, c)
  },
};

export default snapshot;
