<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 28</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#79B0F1" offset="0%"></stop>
            <stop stop-color="#4478DF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="全息档案" transform="translate(-205, -1122)">
            <g id="编组-28" transform="translate(205, 1122)">
                <circle id="椭圆形" fill="url(#linearGradient-1)" cx="20" cy="20" r="20"></circle>
                <g id="总计" transform="translate(10, 10)" fill="#FFFFFF" fill-rule="nonzero">
                    <polygon id="路径" points="20 5.30580357 10 0 0 5.30580357 10 10.609375"></polygon>
                    <polygon id="路径" points="10 12.609375 2.54017857 8.65178571 0 10 10 15.3058036 20 10 17.4598214 8.65178571"></polygon>
                    <polygon id="路径" points="10 17.3058036 2.54017857 13.3482143 0 14.6941964 10 20 20 14.6941964 17.4598214 13.3482143"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>