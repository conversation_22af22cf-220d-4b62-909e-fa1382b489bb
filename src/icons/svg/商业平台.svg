<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 31</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#96F179" offset="0%"></stop>
            <stop stop-color="#47AC1D" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="全息档案" transform="translate(-836, -1122)">
            <g id="编组-27" transform="translate(815.5, 1114)">
                <g id="编组-24" transform="translate(20.5001, 0)">
                    <g id="编组-31" transform="translate(0, 8)">
                        <circle id="椭圆形备份-10" fill="url(#linearGradient-1)" cx="20" cy="20" r="20"></circle>
                        <g id="办公平台" transform="translate(10, 11)" fill="#FFFFFF">
                            <path d="M0,12.652753 C0,13.7223672 0.857127563,14.5963202 1.90047629,14.5963202 L7.62741486,14.5963202 L7.62741486,17.0329535 L4.28308681,17.0329535 C3.76013697,17.0329535 3.32136929,17.4816696 3.32136929,18.0164767 C3.32136929,18.5512838 3.76013697,19 4.28308681,19 L15.7140052,19 C16.236955,19 16.6757227,18.5512838 16.6757227,18.0164767 C16.6757227,17.4816696 16.236955,17.0329535 15.7140052,17.0329535 L12.3722281,17.0329535 L12.3722281,14.5963202 L18.0991667,14.5963202 C19.1450664,14.5963202 19.999643,13.7197583 19.999643,12.652753 L19.999643,11.6692297 L0,11.6692297 L0,12.652753 L0,12.652753 Z M18.1170235,0 L1.90047629,0 C0.854576596,0 0,0.876561864 0,1.9435672 L0,10.706577 L19.999643,10.706577 L19.999643,1.9435672 C20.0200508,0.876561846 19.1629232,0 18.1170235,0 Z" id="形状"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>