import Muuri from 'muuri';
export default {
	//
	// Initialize stuff
	//
	grid : null,
	uuid : 0,
	uuidSort : null,
	updateHandler : null,

	initGrid(queryEL,updateHandler) {
		this.updateHandler = updateHandler;
		let dragCounter = 0;
		let dragMove = 0;
		let $this = this;
		this.grid = new Muuri(document.querySelector(queryEL), {
			items: this.generateElements(queryEL),
			layoutDuration: 400,
			layoutEasing: 'ease',
			dragEnabled: true,
			dragSortInterval: 50,
			dragContainer: document.querySelector(queryEL),
			dragStartPredicate: function(item, event) {
				// 此处可以判定是否触发拖拽,true开始拖拽，false忽略拖拽
				if(event.target.tagName.toLowerCase()!='canvas'){
					return true;
				}
			},
			dragReleaseDuration: 400,
			dragReleseEasing: 'ease'
		})
		.on('dragStart', function() {
			++dragCounter;
			document.documentElement.classList.add('dragging');
		})
		.on('dragEnd', function() {
			if(--dragCounter < 1) {
				document.documentElement.classList.remove('dragging');
			}
			if(dragMove > 0) {
				$this.updateIndices();
				dragMove = 0;
			}
		})
		.on('move', function() {
			dragMove++;
		});
		return this.grid;
	},

	/**
	 * 模块数据生成
	 */
	generateElements(queryEL) {

		let ret = [];
		let itemArray = document.querySelector(queryEL).querySelectorAll(".item");
		
		for(let i = 0; i < itemArray.length ; i++ ){
			let item = itemArray[i];
			
			let dataId = item.getAttribute("data-id");
			
			this.uuidSort = this.uuidSort ? this.uuidSort + ',' + dataId : dataId;
			
			ret.push(item);
		}
		
		return ret;

	},

	/**
	 * 更新排序
	 */
	updateIndices() {
		let newUuidSort;
		this.grid.getItems().forEach(function(item, i) {
			let uuid = item._element.getAttribute("data-id");
			newUuidSort = newUuidSort ? newUuidSort + ',' + uuid : uuid;
		});
		if(this.uuidSort != newUuidSort) {
			// 顺序改变，保存新顺序
			this.uuidSort = newUuidSort;
			if(this.updateHandler){
				this.updateHandler(this.uuidSort);
			}
		}
	}
}
