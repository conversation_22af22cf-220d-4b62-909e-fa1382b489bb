

import Matcher from 'trs-tool-matcher';
/**
 * @Deprecated 已淘汰方法，此处为兼容旧代码。直接使用trs-tool-highlight组件代替。2019/07/08
 *
 * 高亮公共方法，对外提供三种方法：
 * 1、highlight，此方法适用于已知关键词（含有逻辑符号，例如与、或、非），使用关键词对字符串进行高亮操作
 * 2、highlightByWord，此方法适用于已知高亮词的情况下，使用高亮词对字符串进行高亮操作
 * 3、getHighlightKeyword,获取高亮词
 */
export default{

    data(){
        return {};
    },

        /**
         * 获取命中词。返回结果为数组，且数组中词语按照字数由小到大排列
         * @param {待高亮的字符串} source
         * @param {待匹配的关键词} _keywordArr
         */
        getHighlightKeyword(source , _keywordArr){

              let _matchRes = Matcher.match(source , _keywordArr);
              if(_matchRes.isMatched){
                  let hitWords = _matchRes.hitWords;
                  if(hitWords && hitWords.length > 0){
                      hitWords.sort(function(a,b){
                          return b.length - a.length;
                      });
                      return hitWords;
                  }
              }
              return;
        },

        /**
         * 对检索词进行高亮
         * @param {待检测文本} _source
         * @param {搜索词} _searchWord
         * @param {高亮样式名称} _className
         */
        highlightSearchWord(_source, _searchWord, _className) {
          // 待检测文本为空或者检索词为空时直接返回
          if(!_source || !_searchWord){
            return _source;
          }

          let _highlightText = "<i class=\"" + _className + "\">"+_searchWord+'</i>';
          return _source.replace(new RegExp(_searchWord, 'g'), _highlightText);
        },

        /**
         * 对检索词数组进行加高亮
         * @param {待检测文本} _source
         * @param {搜索词数组} _searchWordsArray
         * @param {高亮样式名称} _className
         */
        highlightSearchWordArray(_source, _searchWordsArray, _className) {
          // 待检测文本为空或者检索词为空时直接返回
          if(!_source || _searchWordsArray.length == 0){
            return _source;
          }
          for(let i=0; i<_searchWordsArray.length; i++){
            let _searchword = _searchWordsArray[i];
            let _highlightText = "<i class=\"" + _className + "\">"+_searchword+'</i>';
            _source = _source.replace(new RegExp(_searchword, 'g'), _highlightText);
          }

          return _source;
        },
        

        /**
         * 根据命中词进行高亮
         * 使用样例：highlightByWord('发送到发送美国到了中国',['美国','5G,'中国'] ,'highlight0');
         * @param {字符串} _source
         * @param {高亮词，数组格式} _hitWords
         * @param {高亮样式名称} _className
         */
        highlightByWord(_source , _hitWords , _className){
              if(!_source || !_hitWords || _hitWords.length == 0){
                //不进行高亮操作
                return _source;
              }
              if(_hitWords){
                  // console.log('_hitWords:'+_hitWords);
                  //  source命中关键词
                  return this.highlightKeywords(_source , _hitWords , _className);
              }
              return _source;
        },
        
        /**
         * 根据多个逻辑表达式高亮字符串
         * 使用样例：highlight('发送到发送美国到了中国',['((美国&5G)|中国)'] ,'highlight0');
         * @param {字符串} _source
         * @param {逻辑表达式数组,包含&、|、!等逻辑符} _keywordArr
         * @param {高亮样式} _className
         */
        highlight(_source , _keywordArr , _className){
              if(!_source || !_keywordArr || _keywordArr.length == 0){
                //不进行高亮操作
                return _source;
              }
              // console.log('_source:'+_source);
              let _hitWords = this.getHighlightKeyword(_source , _keywordArr);
              if(_hitWords){
                  // console.log('_hitWords:'+_hitWords);
                  //  source命中关键词
                  return this.highlightKeywords(_source , _hitWords , _className);
              }
              return _source;
        },
        
        /**
         * 内部方法，获取命中词在字符串中的位置（index），针对高亮词的位置进行高亮
         * @param {字符串} _source
         * @param {高亮词，格式为：字符串数组} _hitWords
         * @param {高亮样式} _className
         */
        highlightKeywords(_source , _hitWords , _className){
              // console.log('_hitWords:'+_hitWords);
              let _highlightPos = this.getHighlightPosition(_source , _hitWords);
              if(_highlightPos && _highlightPos.length > 0){
                  //在高亮索引位置处添加高亮标签
                  let startHtml = "<i class=\"" + _className + "\">";
                  let endHtml = "</i>";
                  //startHtml、endHtml分别整体存入数组中，故总长度为2
                  let totalLen = 2;
                  let sourceBuilder = this.getCharArr(_source);
                  if(sourceBuilder && sourceBuilder.length > 0){
                      for(let i = 0 ; i < _highlightPos.length  ; i++){
                        let obj = _highlightPos[i];
                        let highlightStart = obj[0] + i*totalLen ;
                        let highlightEnd = obj[1] + 2 + i*totalLen ;
                        // console.log('highlightStart：'+highlightStart);
                        // console.log('highlightEnd：'+highlightEnd);
                        sourceBuilder.splice(highlightStart, 0 , startHtml);
                        sourceBuilder.splice(highlightEnd,  0 , endHtml);
                      }
                      return sourceBuilder.join('');
                  }
              }
              return _source;
        },
        getCharArr(_str){
              if(_str){
                  let charArr = [];
                  for(let i = 0 ; i < _str.length > 0 ; i++){
                      charArr.push(_str.charAt(i));
                  }
                  return charArr;
              }
              return;
        },
        getHighlightPosition(_source , _hitWords){
              //将_hitWords在_source中的索引全部取出来
              let indexList = [];
              let isStyle = false;
              for(let i = 0 ; i < _source.length ; i++){
                if(_source.charAt(i) === "<"){
                  isStyle = true;
                }
                if(_source.charAt(i) === ">"){
                  isStyle = false;
                }
                if(!isStyle){
                    for(let _wordIndex in _hitWords){
                        let flag = true;
                        let _word = _hitWords[_wordIndex];
                        if(_source.charAt(i) == _word.charAt(0)){
                          for(let j = 0 ; j < _word.length ; j++){
                            if(i+j >= _source.length || _source.charAt(i+j) != _word.charAt(j)){
                              flag = false;
                              break;
                            }
                          }
                        }else{
                          flag = false;
                        }
                        if(flag){
                          for(let j = i ; j <= i+_word.length-1 ; j++){
                            if(indexList.indexOf(j) < 0){//便于合并索引
                              indexList.push(j);
                            }
                          }
                        }
                  }
                }

              }
              //将上步中索引按从小到大排序
              indexList.sort(function(a , b){
                  return a-b;
              });
              // console.log('indexList：'+indexList);
              //将上述字符串按照[开始位置，结束位置]组装
              let sourceList = [];
              let lastEndVal = -1;
              let lastStartVal = -1;
              for(let i = 0 ; i < indexList.length ; i++){
                  let tmp = indexList[i];
                  let _indexInnerArr = [];
                  if(lastStartVal == -1){
                      lastStartVal = tmp;
                      lastEndVal = tmp;
                      if(i == indexList.length - 1){
                          _indexInnerArr.push(lastStartVal);
                          _indexInnerArr.push(lastEndVal);
                      }
                  }else if((lastEndVal+1) !== tmp){
                      _indexInnerArr.push(lastStartVal);
                      _indexInnerArr.push(lastEndVal);
                      lastStartVal = tmp;
                      lastEndVal = tmp;
                      if(i == indexList.length - 1){
                          sourceList.push(_indexInnerArr);
                          _indexInnerArr = [];
                          _indexInnerArr.push(lastStartVal);
                          _indexInnerArr.push(lastEndVal);
                      }
                  }else if((lastEndVal+1) === tmp){
                      lastEndVal = tmp;
                      if(i == indexList.length - 1){
                          _indexInnerArr.push(lastStartVal);
                          _indexInnerArr.push(lastEndVal);
                      }
                  }
                  if( _indexInnerArr.length > 0 ){
                      sourceList.push(_indexInnerArr);
                  }
              }
              // console.log('sourceList:'+sourceList);
              return sourceList;
        },
        /**当有标红和标黄高亮时，解决关键词与检索词有共同字符无法正常高亮，本方法是把已经标红的字符传入，本方法只标黄
        * @param {} test   已经标红的源字符串
         * @param {*} keywordTemp  检索词  字符串
         * <AUTHOR>
         */
        highlightSearchWordTwo(test,keywordTemp){
            if (keywordTemp && test) {
            //在已经标红的字符串中找到检索词的第一字符的索引
            let _highlightPos=this.getHighlightPosition(test,keywordTemp.charAt(0));
            let sourceBuilder = this.getCharArr(test);
            //存放需要标黄的字符索引
            let keywordList=[];
            for(let i = 0 ; i < _highlightPos.length  ; i++){
              let keyList=[];
              let startIndex=_highlightPos[i][0];
              let subTestTemp=test.substring(startIndex,test.length);
              let subTestTemp_=subTestTemp.replace(/<[^>]+>/g,""); //去除html标签，主要是去除标红，找到需要标黄的索引
              if (subTestTemp_.substring(0,keywordTemp.length)===keywordTemp) {
                for (let index = 0; index <keywordTemp.length; index++) {
                  let num=0;
                  let key=keywordTemp.charAt(index);
                  //解决 检索词中有重复的字符时后面出现的字符不会标黄  如 重中之重，第二个重不会标黄
                  keyList.push(key);
                  for (let key_ in keyList) {
                    if (keyList[key_]===key) {
                      num++;
                    }
                  }
                  let x=this.findIndex(subTestTemp,key,num-1);
                  keywordList.push(startIndex+x);
                }
              }
            }
            let startHtml = "<i class=\"highlight5\">";
            let endHtml = "</i>";
            for(let i = 0 ; i < keywordList.length; i++){
              let highlightStart = keywordList[i]+i*2;
              let highlightEnd = keywordList[i]+2+i*2;
              sourceBuilder.splice(highlightStart, 0 , startHtml);
              sourceBuilder.splice(highlightEnd,  0 , endHtml);
           }
           return sourceBuilder.join('');
          } 
          return test;
      },
      //在str中查找第num次出现cha字符的索引位置 num=0为第一次
      findIndex(str,cha,num){
        var x=str.indexOf(cha);
        for(var i=0;i<num;i++){
          x=str.indexOf(cha,x+1);
        }
        return x;
      }


}
