import highlightTool from 'trs-tool-highlight';

/**
 * @Deprecated 直接使用trs-tool-highlight组件，此处为兼容旧代码2019/07/08
 */
export default{
    /**
     * @Deprecated 直接使用trs-tool-highlight组件，此处为兼容旧代码2019/07/08
     */
    highlight(_source , _sqlStr , _className){
        return highlightTool.highlight(_source , _sqlStr , _className);
    },

    /**
     * @Deprecated 直接使用trs-tool-highlight组件，此处为兼容旧代码2019/07/08
     */
    getHitWords(_source , _sqlStr){
        return highlightTool.getHitWords(_source , _sqlStr);
    },

    /**
     * @Deprecated 直接使用trs-tool-highlight组件，此处为兼容旧代码2019/07/08
     */
    highlightByHitWords(_source , _hitWords , _className){
        return highlightTool.highlightByHitWords(_source , _hitWords , _className);
    }
}