import VueResource from "vue-resource";
import iView from "iview";
// import 'iview/dist/styles/iview.css'; // 全局引入  iview CSS
import "../../../static/iview.css"; // 自定义iview CSS
import "../css/theme-center.less"; // 自定义主题
import "../css/iconfont.css";
import "../css/resetting.css";
import "../css/common.less";
import "./iconfont";
// import EasyScroll from 'easyscroll';//引入easyscroll滚动条组件

// 通用配置、函数
export default {
  init(Vue, router) {
    // 关闭控制台环境提示
    Vue.config.productionTip = false;
    // 请求数据资源组件
    Vue.use(VueResource);
    // 启用iview组件
    Vue.use(iView);

    Vue.filter("splitNum", function (num) {
      return Number(num).toLocaleString();
    });
    // 启用EasyScroll组件
    // Vue.use(EasyScroll);
  },
  loadingBar(router) {
    iView.LoadingBar.config({
      color: "#3172eb",
      failedColor: "#fb5718",
      height: 2,
    });
    // 路由切换进度条
    router.beforeEach((to, from, next) => {
      iView.LoadingBar.start();
      next();
    });

    router.afterEach((route) => {
      iView.LoadingBar.finish();
    });
  },
};
