import errorcodeJ<PERSON>N from "@/assets/json/errorcode";

let totalThread = 0;
let threadNum = 0;
let hasError = false;
let noLogin = false;
export default {
  init(Vue, router) {
    // http请求统一拦截处理
    Vue.http.interceptors.push(function (request, next) {
      // console.log(router)
      // 原本逻辑
      if (request.url.indexOf("http") === -1) {
        request.url = gl.serverURL + request.url;
      } else {
        //pad使用
        if (
          window.location.host.startsWith("************") &&
          request.url.indexOf("***********:31443") > -1
        ) {
          request.url = request.url.replace(
            "***********:31443",
            "************:31443"
          );
        }
      }
      if (gl.develop && gl.DEV_TOKEN) {
        request.headers.set("token", gl.DEV_TOKEN);
        localStorage.setItem("tokens", gl.DEV_TOKEN);
      }

      const currentRuote = router.currentRoute
      if((currentRuote.meta && currentRuote.meta.moduleName) || (currentRuote.query.moduleName)){
        const sys_log_module = currentRuote.query.moduleName ? encodeURIComponent(decodeURIComponent(currentRuote.query.moduleName)) : encodeURIComponent(currentRuote.meta.moduleName)
        request.headers.set("sys_log_module", sys_log_module);
      }

      if (totalThread === 0) {
        // 进度条开始
        this.$Loading.start();
      }
      totalThread++;
      threadNum++; //未完成的请求

      if (request && request.params) {
        // 请求前处理增加时间标记缓存...
        request.params["_"] = new Date().getTime();
      }
      //------------------------------------------------------------------
      let TOKEN = localStorage.getItem("tokens");
      // let otherUserLogin = (localStorage.getItem("userId") && this.$cookies.get("tsLoginUserId") != localStorage.getItem('userId'));
      // // TOKEN存在 是否有别的用户登录
      if (TOKEN && request.url.indexOf("/extApi/findDept") == -1) {
        // 就为每次请求的headers中设置好TOKEN, 后台根据headers中的TOKEN判断是否登录
        request.headers.set("token", TOKEN);
      }
      //------------------------------------------------------------------
      next(function (response) {
        // 请求结果拦截处理
        threadNum--;
        let status = response.body.status;
        if (!this.$route?.meta?.noRequiresAuth && this.$route?.path !== "/") {
          if (status == errorcodeJSON.login) {
            if (
              process.env.NODE_ENV == "production" &&
              $route.path.indexOf("/main/details") == -1
            )
              window.location.href = process.env.HOME_WEB;
          } else if (status == errorcodeJSON.previlige) {
            this.$Message.error(response.body.message);
          } else if (status == errorcodeJSON.yunwei) {
            window.location.href =
              gl.baseURI + "/" + "?t=" + new Date().getTime();
          }
        }

        // 进度条处理
        if (response.status != 200 || (status != null && status != 0)) {
          console.log("error:", response);
          this.$Loading.error();
          hasError = true;
        }
        if (threadNum == 0) {
          if (!hasError) this.$Loading.finish();
          hasError = false;
          threadNum = 0;
          totalThread = 0;
        } else if (!hasError) {
          let percent = ((totalThread - threadNum) / totalThread) * 100;
          this.$Loading.update(percent);
        }
      });
    });

    Vue.prototype.hasPermission = function (target, resourceArray) {
      if (gl.develop) return true;

      // 返回处理后的值
      let isHavePermission = false;

      if (!resourceArray) {
        let resources = localStorage.getItem("resources");
        try {
          resourceArray = JSON.parse(resources);
        } catch (e) {
          console.error(
            "本地权限解析异常，清空存储。错误资源信息resources:" + resources
          );
          resourceArray = [];
          localStorage.removeItem("resources");
        }
      }

      if (resourceArray) {
        for (let urlObj in resourceArray) {
          let resourceExists = resourceArray[urlObj];
          if (resourceExists) {
            if (
              resourceExists.permissionNewUrl === target ||
              resourceExists.url === target
            ) {
              isHavePermission = true;
            } else if (resourceExists.children) {
              isHavePermission = this.hasPermission(
                target,
                resourceExists.children
              );
            }
            if (isHavePermission) {
              return true;
            }
          }
        }
      }
      return false;
    };
  },
};
