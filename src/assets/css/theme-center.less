@charset "utf-8";
@import "./theme-variate";

body { // 默认样式
  font-size: 14px;
}

/* 隐藏样式 */
.hide {
  display: none;
}

/* loading 样式*/
.ivu-spin-fix {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 对话框样式 */
.ivu-modal-mask {
  background-color: rgba(55, 55, 55, 0);

  .ivu-modal-wrap {
    z-index: @zindex-modal;
  }
}

.ivu-modal-wrap {
  z-index: @zindex-modal !important;
}

.ivu-modal-content {
  border-radius: 3px;
  box-shadow: 0px 3px 10px #d5d5d5;
}

.ivu-modal-header {
  background: linear-gradient(to right, #333173, #316CDF);
  padding-top: 0px;
  padding-bottom: 0px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;

  .ivu-modal-header p, .ivu-modal-header-inner {
    color: white;
    text-align: center;
    height: 80px;
    line-height: 80px;
    font-weight: 600;
    font-size: 24px;
  }
}

.ivu-modal-close {
  top: 0;

  .ivu-icon-ios-close-empty {
    color: white;
  }
}

.ivu-modal-footer {
  border-top: 0px;
  text-align: center;

  button.ivu-btn {
    width: 75px;
    height: 30px;
    padding-top: 3px;
  }

  .ivu-btn-text {
    border: 1px #89898E solid;
  }
}

.ivu-form-item {
  margin-bottom: 4px;
  vertical-align: top;
  zoom: 1;
}

.ivu-input-wrapper {
  .ivu-input-icon-normal + .ivu-input {
    //  border: 1px #1890FF solid;
    padding: 4px 30px 4px 7px;
    height: 32px;
    line-height: 32px;
    border-radius: 2px;
  }
}

.ivu-icon-ios-search {
  //background-color: #1890FF;
  // color: white;
  width: 33px;
  height: 32px;
  line-height: 30px;
  font-size: 14px;
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px;
  cursor: pointer;
}

/* 默认按钮样式*/
button.ivu-btn {
  height: 30px;
  line-height: 14px;
  padding: 3px 10px;
}

.shadow {
  background: white;
  border-radius: 3px;
  box-shadow: 0px 3px 10px #d5d5d5;
}

.loading.fix {
  position: absolute;
  height: 100%;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.5);

  &.transparent {
    background-color: transparent;
  }

  .ivu-spin-fix {
    background-color: transparent;
  }
}

i.highlight0 {
  color: red;
  font-style: normal;
}

i.highlight5 {
  background: yellow;
  font-style: normal;
}

.nodata {
  // background:url(../img/monitor/nodata.png) no-repeat center center;
  background-size: auto 70%;
  width: 100%;
  height: 100%;
  min-height: 200px;
  text-align: center;
  font-size: 26px;
  line-height: 100px;
  color: #495060;
}

button.nav_btn {
  min-width: 80px;
  height: auto;
  padding: 0 10px 0 10px;
  background-color: transparent;
  color: #000;
  margin-right: 16px;
  border: 0;
  border-radius: 0;

  &.active, &:hover {
    background-color: transparent;
    color: @btn-primary-bg;
  }

  & + button.nav_btn {
    border-left: 1px rgb(206, 206, 199) solid;
    padding-left: 30px;
  }

  + hr {
    margin-top: 17px;
    border: 0px;
    border-top: 1px rgb(223, 228, 239) solid;
  }
}

.ivu-page-item-jump-next + li.ivu-page-item {
  display: none;
}

.clear {
  clear: both;
}

//版本升级后，box-shadow问题
a.ivu-btn-text:focus, .ivu-btn-text:focus, .ivu-btn-primary:focus {
  box-shadow: none;
}

label.ivu-checkbox-wrapper.ivu-checkbox-group-item :nth-child(2) {
  display: none;
}

//tab-nav outline问题
.ivu-tabs-nav-container {
  outline: none;
}

ol, ul {
  list-style: none;
  list-style-type: none;
  list-style-position: initial;
  list-style-image: initial;
}
