@text-color: #5a5a5a;
@left-menu-background-color: #EFF3F6;
@split-color: #E8E8E8; //分割线颜色
@base-font-size: 14px;

.base-width {
  width: 1200px;
  margin: 0 auto;
}

.ivu-message {
  z-index: 999999 !important;
  /* 确保这个值比其他元素的 z-index 大 */
}

.ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  /* 控制显示的行数 */
}

.ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 3;
  /* 控制显示的行数 */
}

.ellipsis-4 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 4;
  /* 控制显示的行数 */
}

.ivu-page {
  text-align: center;
  padding: 10px;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.highlight0 {
  color: red;
}

.flex {
  display: flex;
}

.no-wrap {
  white-space: nowrap;
}

.sb {
  justify-content: space-between;
}

.rotate-180 {
  transform: rotate(180deg);
}

.cp {
  cursor: pointer;
}

.btns,
.ListControls .item {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0 5px;
  width: auto !important;
  white-space: nowrap;

  svg {
    margin-right: 3px;
  }
}

.btns:hover,
.ListControls .item:hover {
  background-color: #cfecf9 !important;
}

.btns:active,
.ListControls .item:active {
  background-color: #799ff3 !important;
  color: #fff !important;
}

.btn {
  text-align: center;
  line-height: 30px;
  font-weight: 600;
  color: #ffffff;
  font-size: 14px;
  min-width: 80px;
  height: 30px;
  border-radius: 4px;
  background: #5585ec;
  cursor: pointer;
}