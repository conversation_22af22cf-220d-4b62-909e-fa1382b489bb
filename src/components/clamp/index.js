export default {
  bind(el, binding) {
    function clamp() {
      const lineHeight = parseInt(window.getComputedStyle(el).lineHeight, 10);
      const maxHeight = lineHeight * binding.value;
      el.style.maxHeight = `${maxHeight}px`;
      el.style.overflow = "hidden";
      el.style.textOverflow = "ellipsis";
      el.style.display = "-webkit-box";
      el.style.webkitBoxOrient = "vertical";
      el.style.webkitLineClamp = binding.value;
    }

    clamp();
    window.addEventListener("resize", clamp);
    el._onResize = clamp; // Store the handler to remove it later
  },
  unbind(el) {
    window.removeEventListener("resize", el._onResize);
  },
};
