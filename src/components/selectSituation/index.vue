<template>
  <div class="selectSituation">
    <div
      :class="['item', selectId === k ? 'active' : '']"
      v-for="(v, k) in list"
      :key="k"
      @click="change(k)"
      :style="v === '风险点报送'? 'margin-left:20px;' : ''"
    >
      <span class="xian" v-show="v === '风险点报送' "></span>
      {{ v }}
      <div class="triangle-down"></div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    list: {
      default: {}
    },
    selectId: {
      default: null
    }
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    change(d) {
      this.$emit("change", d);
    }
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {}
};
</script>

<style scoped lang="less">
.selectSituation {
  padding: 20px 0 0 0;
  display: flex;

  .item {
    width: 160px;
    height: 40px;
    background: #ebedf8;
    border-radius: 4px;
    box-shadow: 0 3px 6px rgba(80, 80, 80, 0.22);
    margin-right: 18px;
    line-height: 40px;
    text-align: center;
    color: #666;
    font-size: 16px;
    position: relative;
    cursor: pointer;

    .triangle-down {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: -8px;
      width: 0;
      height: 0;
      border-left: 9px solid transparent;
      border-right: 9px solid transparent;
      border-top: 8px solid #5585ec;
      display: none;
    }
    .xian {
      display: inline-block;
      width: 1px;
      height: 40px;
      background: #a9c5f4;
      position: absolute;
      left: -20px;
    }
  }

  .active {
    background: #5585ec;
    font-weight: 600;
    color: #ffffff;

    .triangle-down {
      display: block;
    }
  }
}
</style>
