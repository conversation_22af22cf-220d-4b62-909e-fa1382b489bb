<template>
  <div class="OriginalText">
    <div class="header flex">
      <div class="avatar">
        <!--        <img src="" alt="" />-->
        <!--        <svg-icon icon-class="user" />-->
        <!--        <Icon style="font-size: 30px" type="md-person" />-->
        <PlatformIcon
          size="60"
          :id="data.situation"
          :text="data.mwebsiteName"
        />
      </div>
      <div class="name">
        <div class="text">{{ data.mwebsiteName || data.uname }}</div>
        <div class="time">
          {{ moment(data.mpublishTime).format("YYYY-MM-DD HH:mm:ss") }}
        </div>
      </div>
    </div>
    <div class="content">
      <div class="text">
        <div v-html="data.mhtmlContent || data.mabstract"></div>
      </div>
      <!--      图标-->
      <!--      <div class="images">-->
      <!--        <img-->
      <!--          v-for="i in 3"-->
      <!--          src="https://tva3.sinaimg.cn/large/006ARE9vgy1fv5kripd7rj31jk1jk1ky.jpg"-->
      <!--          alt=""-->
      <!--        />-->
      <!--      </div>-->
    </div>
    <!-- data.situation === 10  -->
    <div v-if="false" class="communication">
      <Tabs v-model="tabId">
        <TabPane icon="ios-share-alt-outline" label="评论" name="1"></TabPane>
        <TabPane icon="ios-mail-outline" label="转发" name="2"></TabPane>
      </Tabs>
      <div class="listBox">
        <div class="item flex">
          <div class="avatar">
            <!--        <img src="" alt="" />-->
            <!--        <svg-icon icon-class="user" />-->
            <Icon style="font-size: 30px" type="md-person" />
          </div>
          <div class="content flex sb">
            <div class="text">
              <span class="nickname"> 别说话，吻我： </span>
              <span class="review ellipsis">滴滴滴滴滴滴滴滴</span>
            </div>
            <div class="remark flex sb">
              <div class="time">2020-2-2 22:22:22</div>
              <div class="num">
                <span>
                  <svg-icon icon-class="涉济报送-转发" />
                  200
                </span>
                <span> <svg-icon icon-class="涉济报送-信息" />200 </span>
                <span> <svg-icon icon-class="点赞" />200 </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import moment from "moment";
import PlatformIcon from "@/components/platformIcon/index.vue";

export default {
  data() {
    // 这里存放数据
    return {
      tabId: "1",
      moment,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { PlatformIcon },
  props: {
    data: {},
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.getForwardCount();
  },
  // 方法集合
  methods: {
    getForwardCount() {
      console.log(this.data);
      let params = {
        msgKey: this.data.mkey,
        type: this.tabId === "2" ? "forward" : "reply", //forward:转发； reply:评论
        publishTime: this.data.mpublishTime,
      };
      this.$http.get("/search/forwardCount", { params }).then((res) => {
        console.log(res);
      });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style lang="less" scoped>
.OriginalText {
  .header {
    .avatar {
      width: 60px;
      height: 60px;
      text-align: center;
      line-height: 60px;
    }

    .name {
      padding-left: 20px;
      flex: 1;

      .text {
        font-weight: 600;
        color: #333333;
        font-size: 20px;
        line-height: 28px;
      }

      .time {
        color: #333333;
        font-size: 14px;
      }
    }
  }

  .content {
    padding-left: 80px;

    .text {
    }

    .images {
      margin-top: 20px;

      img {
        width: 140px;
        height: 180px;
        border: 1px solid #707070;
        border-radius: 4px;
        object-fit: cover;
        margin-right: 10px;
      }
    }
  }

  .communication {
    .listBox {
      padding-left: 80px;

      .item {
        .avatar {
          height: 50px;
          width: 50px;
          background-color: #00b1ff;
          text-align: center;
          line-height: 50px;
          border-radius: 50%;
        }

        .content {
          flex: 1;
          padding: 3px 50px 3px 10px;
          color: #707070;
          flex-direction: column;
          line-height: 22px;

          .nickname {
            font-weight: 600;
            color: #f65177;
            font-size: 16px;
          }
        }
      }
    }
  }
}

/deep/ .ivu-tabs-nav-scroll {
  display: flex;
  justify-content: center;
}
</style>
