<template>
  <svg-icon
    :icon-class="websiteName.includes(text) ? text : situationIcon[id]"
    :style="{ width: size + 'px', height: size + 'px' }"
  />
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
const situationIcon = {
  30: "新闻网站",
  31: "新闻APP",
  10: "微博",
  20: "微信公众号",
  61: "论坛",
  62: "贴吧",
  170: "知乎",
  41: "论坛跟帖",
  51: "贴吧跟帖",
  80: "小红书",
  48: "视频",
  50: "视频",
  110: "火山",
  188: "视频",
  199: "视频",
  30: "新闻",
  31: "新闻app",
  10: "微博",
  20: "微信公号",
  80: "小红书",
  60: "论坛",
  61: "论坛",
  62: "贴吧",
  170: "知乎",
  230: "自媒体",
  300: "视频300",
  200: "视频",
  320: "新闻网站",
  400: "境外新闻网站",
  501: "Facebook",
  502: "推特",
  600: "今日头条",
};

export default {
  data() {
    // 这里存放数据
    return {
      situationIcon,
      websiteName: ["抖音", "快手", "今日头条", "西瓜视频"],
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    id: {
      default: 10,
    },
    size: {
      default: 16,
    },
    text: {
      default: "",
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {},
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style lang="less" scoped></style>
