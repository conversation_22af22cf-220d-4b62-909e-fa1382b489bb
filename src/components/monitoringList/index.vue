<template>
  <div class="monitoringList">
    <div class="title">
      监测列表
    </div>
    <router-link :to="'/main/monitorInfo' + '?type=' + type" target="_blank">
      <div class="addMonitor">
        <svg-icon icon-class="监测列表-新建" />
        新建
      </div>
    </router-link>
    <div class="content">
      <div class="group" v-for="(i, index) in list" :key="index">
        <div class="groupTitle" @click="unpack(index)">
          {{ i.name }}
          <Icon :type="i.unpack ? 'md-arrow-dropup' : 'md-arrow-dropdown'" />
        </div>
        <div
          v-if="i.objs"
          :class="['subclass', i.unpack ? 'unfold' : 'packUp']"
        >
          <div
            :class="[
              'item',
              'ellipsis',
              selectId === item.objId ? 'active' : '',
            ]"
            v-for="item in i.objs"
            :key="item.objId"
          >
            <span class="text" @click="changeObjId(item)">{{ item.name }}</span>
            <div class="option">
              <router-link
                :to="
                  '/main/monitorInfo?source=' +
                    $route.path +
                    '&objId=' +
                    item.objId +
                    '&type=' +
                    type
                "
                target="_blank"
              >
                <svg-icon
                  icon-class="监测列表-编辑"
                  @click.native="edit(item.objId)"
                />
              </router-link>
              <Poptip
                transfer
                confirm
                title="是否确认删除该监测?"
                @on-ok="del(item.objId)"
              >
                <svg-icon icon-class="榜单-删除" />
              </Poptip>
            </div>
          </div>
        </div>
      </div>
      <!-- 区县监测列表 -->
      <div class="districtAndCountyMonitoring" v-if="districtAndCountyList && districtAndCountyList.length > 0">
          <div class="title">
              区县监测列表
          </div>
          <div class="group" v-for="(i, index) in districtAndCountyList" :key="index">
            <div :class="['groupTitle', i.unpack ? 'zhankai' : 'shouqi']" @click="districtUnpack(index)">
              {{ i.organName }}
              <Icon :type="i.unpack ? 'md-arrow-dropup' : 'md-arrow-dropdown'" />
            </div>
            <div
              v-if="i.objs"
              :class="['subclass', i.unpack ? 'unfold' : 'packUp']"
            >
              <div
                :class="[
                  'item',
                  'ellipsis',
                  selectId === item.objId ? 'active' : '',
                ]"
                v-for="item in i.objs"
                :key="item.objId"
              >
                <span class="text" @click="changeObjId(item, true)">{{ item.name }}</span>
                <div class="option">
                  <router-link
                    :to="
                      '/main/monitorInfo?source=' +
                        $route.path +
                        '&objId=' +
                        item.objId +
                        '&type=' +
                        type + '&isQx=1'
                    "
                    target="_blank"
                  >
                    <svg-icon
                      icon-class="详情"
                      @click.native="edit(item.objId)"
                    />
                  </router-link>
                </div>
              </div>
            </div>
          </div>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  data() {
    // 这里存放数据
    return {
      unpackId: null,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    list: {
      default: [{ unpack: true }],
    },
    selectId: {
      default: "",
    },
    type: {
      default: null,
    },
    districtAndCountyList: {
      default: []
    }
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    unpack(index) {
      let i = this.list[index];
      i.unpack = !i.unpack;
      this.$set(this.list, index, i);
    },
    districtUnpack(index){
      let i = this.districtAndCountyList[index];
      i.unpack = !i.unpack;
      this.$set(this.districtAndCountyList, index, i);
      if(i.unpack && i.objs.length == 0){
        this.getObjs(i.organId, index)
      }
    },
    getObjs(organId, index){
      this.$http.get("/monitor/sdzbObjList", {
          params: {
              type: 2,
              organId: organId
          },
      }).then((res) => {
        const objList = res.body.data || []
        const objs = objList.reduce((pre, current) => {
          if(current.objs && current.objs.length > 0){
            return pre.concat(current.objs)
          }else {
            return pre
          }
        }, [])
        this.districtAndCountyList[index].objs = objs
        this.$set(this.districtAndCountyList, index, this.districtAndCountyList[index]);
      })
    },
    changeObjId(d, type) {
      this.$emit("change", d, type);
    },
    edit(d) {},
    del(d) {
      this.$emit("del", d);
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    list: {
      handler(d) {
        //this.unpackId = d[0].classifyId;
      },
    },
  },
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style scoped lang="less">
.monitoringList {
  height: 100%;
  width: 338px;
  background-color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.29);
  border-radius: 8px;
  padding: 10px 23px;
  position: relative;

  .title {
    color: #333333;
    font-size: 18px;
    text-align: center;
  }

  .addMonitor {
    color: #ffffff;
    font-size: 16px;
    width: 78px;
    height: 26px;
    background: #5585ec;
    border-radius: 3px;
    line-height: 26px;
    text-align: center;
    position: absolute;
    right: 23px;
    top: 10px;
    cursor: pointer;
  }

  .content {
    height: calc(~"100% - 30px");
    overflow-y: auto;
    margin: 10px -3px 10px 0;
    padding-right: 3px;

    .group {
      background: #f5f8fe;
      border-radius: 8px;
      margin-bottom: 10px;

      .groupTitle {
        background: #5585ec;
        border-radius: 8px;
        height: 60px;
        width: 100%;
        position: relative;
        text-align: center;
        color: #fff;
        line-height: 60px;
        cursor: pointer;

        /deep/ .ivu-icon {
          color: #fff;
          font-size: 36px;
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translatey(-50%);
        }
      }
    }

    .packUp {
      height: 0;
      padding: 0 !important;
      overflow: hidden;
    }

    .subclass {
      border-radius: 8px;
      margin-top: -4px;
      padding: 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      transition: height 0.5s;

      .item {
        padding: 0 50px;
        width: 269px;
        height: 37px;
        line-height: 37px;
        margin: 3px 0;
        text-align: center;
        color: #333333;
        font-size: 16px;
        position: relative;

        .text {
          cursor: pointer;
        }

        .option {
          position: absolute;
          right: 5px;
          top: 50%;
          transform: translatey(-50%);

          .svg-icon {
            margin: 0 2px;
            cursor: pointer;
            height: 14px;
          }
        }
      }

      .active {
        background: #c1d3f9;
        border-radius: 4px;
      }
    }

    .districtAndCountyMonitoring{
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px dashed #5585ec;
      user-select: none;
      .group{
        margin-top: 10px;
        .groupTitle{
           border: 1px solid rgba(85, 133, 286, 0.3);
          background-color: #fff;
          color: #333;
          /deep/ .ivu-icon {
            color: #333;
          }
          &.zhankai{
            border: 1px solid #5585ec;
            color: #5585ec;
            /deep/ .ivu-icon {
              color: #5585ec;
            }
          }
        }
        
      }
    }

  }
}

</style>
