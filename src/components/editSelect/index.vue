<template>
    <div style="position: relative" class="custom-select-dropdown">
        <Input v-model="keyWord" :placeholder="placeholder" @on-focus="focusNotarialCode"
            @keydown.enter.native="searchButton">
        <Button slot="append" icon="ios-search" @click="searchButton"></Button>
        </Input>
        <div v-show="isShowDrownItem && optionList && optionList.length > 0" class="ivu-select-dropdown"
            style="width: 100%">
            <ul class="ivu-select-dropdown-list">
                <li v-for="(item, idx) in optionList" :key="item.value + idx" class="ivu-select-item"><span
                        @click="selectedItem(item)">{{ item.label }}</span> <img src="../../assets/img/摄像头.png" width="20"
                        height="20" @click="openVideo(item)" />
                </li>
            </ul>

            <Spin v-show="loading" fix>
                <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
                <div>Loading</div>
            </Spin>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CustomSelect',
    props: {
        selected: {
            type: [String, Number]
        },
        selectKeyword: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: ''
        },
        loading: {
            type: Boolean,
            default: false
        },
        keyName: {
            type: String
        },
        optionList: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    data() {
        return {
            isShowDrownItem: false,
            keyWord: ''
        }
    },
    watch: {
        optionList(newVal) {
            console.log('optionList', newVal)
        },
        selected: {
            immediate: true,
            handler(newVal) {
                console.log('selected', newVal)
                this.keyWord = newVal
            }
        }
    },
    mounted() {
        this.keyWord = this.selectKeyword;
        document.addEventListener('click', this.hidePopClick)
    },
    beforeDestroy() {
        document.removeEventListener('click', this.hidePopClick)
    },
    methods: {
        hidePopClick(e) {
            const path = e.path || (e.composedPath && e.composedPath())
            const flag = path.some(dom => dom.className && dom.className.indexOf('custom-select-dropdown') !== -1)
            if (!flag) {
                this.isShowDrownItem = false
            }
        },
        focusNotarialCode() {
            this.isShowDrownItem = true
        },
        selectedItem(item) {
            console.log('选中', item)
            this.keyWord = this.keyName ? item[this.keyName] : item.label
            this.$emit('selectedItem', item)
            this.isShowDrownItem = false
        },
        openVideo(item) {
            this.$emit("openVideo", item.cameraIndexCode);
        },
        searchButton() {
            this.isShowDrownItem = true;
            this.$emit("buttonClick", this.keyWord);
        }
    }
}
</script>

<style scoped></style>

