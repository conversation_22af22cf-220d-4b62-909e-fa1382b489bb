<!-- 入口页 -->
<template>
  <div class="conts-zong">
    <div class="contents" :style="type == 2 ? 'width:600px;' : ''">
      <div
        v-for="(val, key) in newData"
        :key="key"
        style="width: 100px; height: 30px; text-align: left"
      >
        <CheckboxGroup v-model="social"
          ><Checkbox :label="key"></Checkbox>{{ val }}
        </CheckboxGroup>
      </div>
    </div>
    <div class="new-btns">
      <span @click.stop="close">取消</span>
      <span style="margin-left: 20px" @click.stop="social = []">清空</span>
      <span style="margin-left: 20px; background: #5585ec" @click.stop="submit"
        >保存</span
      >
    </div>
  </div>
</template>

<script>
export default {
  props: {
    modalData: {
      default: () => {},
    },
    selecId: {
      default: () => [],
    },
    type: {},
  },
  data() {
    return {
      newData: {},
      social: [],
    };
  },
  watch: {
    modalData: {
      handler(val) {
        this.newData = val;
      },
      deep: true,
      immediate: true,
    },
    selecId: {
      handler(val) {
        console.log(val);
        if (val.length > 0 && (val != "其他" || val != "未分类")) {
          this.social = val.split(",");
          console.log(this.social, "this.social");
        }
      },
      deep: true,
      immediate: true,
    },
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {},
  //方法所在
  methods: {
    submit() {
      this.$emit("submit", this.type, this.social);
    },
    close() {
      this.$emit("close");
    },
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {},
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.conts-zong {
  position: absolute;
  background: #fff;
  border: 1px solid #e1d9d9;
  border-radius: 2px;
  padding: 10px 20px 20px 20px;
  top: 11px;
  z-index: 1;
}
.contents {
  display: flex;
  flex-wrap: wrap;
  width: 400px;
  justify-content: left;
  position: relative;
  margin-bottom: 20px;
}
.new-btns {
  position: relative;
  span {
    display: inline-block;
    width: 90px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 4px;
    background: #999;
    color: #fff;
    cursor: pointer;
  }
}
</style>
