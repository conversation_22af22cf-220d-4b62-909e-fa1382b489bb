<!-- 入口页 -->
<template>
  <div class="conts-zong">
    <div class="contents" :style="type == 2 ? 'min-width:1000px;':''"> 
        <div v-for="(k) in oneList" :key="k.id"  class="width100" :title="k.name" style="fong-weight:600;">
        <CheckboxGroup v-model="social" @on-change="handleChange"><Checkbox :label="k.name"></Checkbox>{{k.name}}
        </CheckboxGroup></div>
    </div>
     <div v-for="(d) in twoList" :key="d.id" class="cs">
           <span class="cs-title" :title="d.name"><span class="name">{{d.name}}</span> <span style="margin-right:10px;">
            <Icon type="ios-arrow-forward"></Icon></span></span>  
            <div style="display:flex; flex-wrap: wrap;flex:1;">
             
              <span v-for="(c) in d.children" :key="c.id" class="cs-content">
                    <CheckboxGroup v-model="social" @on-change="handleChange">
                    <Checkbox :label="c.name"></Checkbox>{{c.name}}
                    </CheckboxGroup></span>
            </div>
               
        
        </div>
    <div class="new-btns"> 
            <span @click="close">取消</span> <span style="margin-left:20px;" @click="social = []">清空</span> <span style="margin-left:20px;background:#5585ec;" @click="submit">保存</span>
         </div>
  </div>
</template>

<script>
export default {
  props: {
    modalData: {
      default: () => {},
    },
    selecId:{
      default:() => []
    },
    type:{}
  },
  data() {
    return {
      oneList:[],
      twoList:[],
      social:[],
      };
  },
  watch: {
    modalData: {
      handler(val) {
        this.oneList = val.filter(v => v.children.length == 0 || !v.children)
        this.twoList = val.filter(v => v.children.length > 0)
      },
      deep: true,
      immediate: true,
    },
    selecId:{
        handler(val) {
        if ((val  != '其他' || val  != '未分类') && val) {
          console.log(val,'44444444');
            let list = val.split(',');
            let newList = []
            list.forEach(v => {
                if (v.indexOf('-')>-1) {
                    newList.push(v.substring(v.indexOf('-') + 1,v.length))
                }else {
                  newList.push(v)
                }
            })
            if (newList.length > 0) {
                this.social = newList
            }else {
                this.social = list;
            }
            this.getSort()
        }
      },
      deep: true,
      immediate: true,
    }
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {},
  //方法所在
  methods: {
    // 去重舜网推送领域
    getSort() {
      let twoName = this.twoList.map(t => t = t.name)
      this.social = this.social.filter(d => twoName.indexOf(d) == -1)
    },
     handleChange(val) {
      // this.selected = value;
      this.social = []
      this.social.push(val[val.length - 1])
    },
    renderContent(h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        }
      }, [
        h('span', [
          h('Icon', {
            props: {
              type: 'ios-folder-outline'
            },
            style: {
              marginRight: '8px',
              color: node.selected ? '#2D8CF0' : '#2D8CF0'
            }
          }),
          h('span', data.title)
        ])
      ]);
    },
    submit() {
         this.$emit('submit',this.type,this.social)
    },
    close() {
      console.log('232432434232000',this.$parent);
        this.$emit('close')
    },
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {},
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.conts-zong {
    position: absolute;
    background: #fff;
    border: 1px solid #e1d9d9;
    border-radius: 2px;
    padding: 10px 20px 20px 20px;
    top: 11px;
    z-index: 1;
}
.contents {
    display: flex;
    flex-wrap: wrap;
    width: 400px;
    justify-content: left;
    position: relative;
    .width100 {
     width: 120px;
     height: 30px;
     text-align: left;
    }
    /deep/.ivu-checkbox-group{
      white-space: nowrap;
     text-overflow: ellipsis;
     overflow: hidden;
     font-weight: 600;
     }
}
 
.cs {
    display: flex;
     position: relative;
     min-height: 30px;
    .cs-title {
        width: 120px;
        display: flex;
        justify-content: space-between;
       .name {
        width: 100px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        text-align: left;
        font-size: 14px;
        font-weight: 600;
       }
    }
    .cs-ding {
            position: absolute;
            left: 100px;
        }
        .cs-content {
            width: 120px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            height: 30px;
            text-align: left;
        }
         /deep/.ivu-checkbox-wrapper {
            font-size: 14px !important;
        }
       
}

  .new-btns {
    // position: relative;
    margin-top: 30px;
    text-align: center;
        span {
            display: inline-block;
            width: 90px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 4px;
            background: #999;
            color: #fff;
            cursor: pointer;
        }
    }
</style>
