<template>
  <div class="tag flex">
    <div class="area" @click="showData(1)">
      <!--      {{ data.marea ? JSON.parse(data.marea).join(",") : "" }}-->
      {{ data.marea ? handleJSON(data.marea, "其他") : "其他" }}
    </div>
    <div class="line"></div>
    <div class="type" @click="showData(2)">
      {{ data.scopeArea ? handleJSON(data.scopeArea, "未分类") : "未分类" }}
    </div>
    <div class="line"></div>
    <div :class="[
      'emotion',
      data.msentiment === 1
        ? 'negative'
        : data.msentiment === 0
          ? 'neutral'
          : 'front',
    ]" @click="handelNewModal">
      {{ data.msentiment === 1 ? "正" : data.msentiment === 0 ? "中" : "负" }}
      <div class="gs" v-if="modalNew">
        <div class="gs-list" @click="handelMsent(1)">正面</div>
        <div class="gs-listOne" @click="handelMsent(0)">中性</div>
        <div class="gs-listTw" @click="handelMsent(-1)">负面</div>
      </div>
    </div>
    <div v-if="data.isMonitor" class="line"></div>
    <div v-if="data.isMonitor" class="type">
      <svg-icon icon-class="摄像头" />
    </div>
    <!--    <div class="line"></div>-->
    <!--    <div class="edit cp">-->
    <!--      <svg-icon icon-class="监测列表-编辑" />-->
    <!--    </div>-->

    <div v-if="modal" style="position: absolute">
      <List :modalData="modalData" @close="close" :type="modalId" @submit="handleSubmit" :selecId="selecId"
        v-if="modalId == 1" />
      <Listnew :modalData="modalData" @close="close" :type="modalId" @submit="handleSubmit" :selecId="selecId"
        v-if="modalId == 2" />
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import List from "./component/list.vue"; //编辑框界面
import Listnew from "./component/domailist.vue"; //编辑框界面
import regionals from "@/assets/json/regionals.json";
import fields from "@/assets/json/fields.json";
import { useDomainStore } from "@/stores/domainList";
import { mapState } from "pinia";

export default {
  data() {
    // 这里存放数据
    return {
      modal: false, //下拉框展示
      modalNew: false, //下拉框展示
      modalData: {}, //下拉框数据
      modalId: 1,
      regionals,
      fields,
      selecId: [],
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { List, Listnew },
  props: {
    data: {
      default: () => {
        return {
          marea: '["济南市"]',
          scopeArea:
            // "['突发事件','教育领域','文体旅游','政策法规','经济金融','城建房产','涉法涉诉']",
            '["济南市"]',
          msentiment: 1,
        };
      },
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() { },
  // 方法集合
  methods: {
    // 展示程度
    handelNewModal() {
      this.modalNew = !this.modalNew;
      this.modal = false;
    },
    handelMsent(id) {
      this.handleSubmit(3, null, id);
    },
    handleSubmit(type, val, id) {
      let url = "/search/updateLabel";
      let params = {
        situation: this.data.situation,
        mkey: this.data.mkey
      };
      if (type == 1) {
        if (val.length == 0) {
          params.marea = "其他";
        } else {
          if (val.indexOf("其他") > -1) {
            val.splice(val.indexOf("其他"), 1);
          }
          params.marea = val.toString();
        }
      } else if (type == 2) {
        if (val.length == 0) {
          params.scopeArea = "未分类";
        } else {
          if (val.indexOf("未分类") > -1) {
            val.splice(val.indexOf("未分类"), 1);
          }
          params.scopeArea = val.toString();
        }
      } else if (type == 3) {
        params.msentiment = id;
      }
      this.$http.post(url, params, { emulateJSON: true }).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          if (type == 1) {
            this.data.marea =
              val.length != 0 ? JSON.stringify(val) : '["其他"]';
          } else if (type == 2) {
            this.data.scopeArea =
              val.length != 0 ? JSON.stringify(val) : '["未分类"]';
          } else if (type == 3) {
            this.data.msentiment = id;
          }
          this.close();
        } else {
          this.$Message.error(result.message);
        }
      });
    },
    close() {
      this.modal = false;
    },
    showData(d) {
      this.modal = true;
      if (d == 1) {
        this.modalId = 1;
        this.modalData = this.regionals;
        this.selecId = this.handleJSON(this.data.marea);
        Reflect.deleteProperty(this.modalData, "0");
      } else {
        this.modalId = 2;
        this.modalData = this.domainList;
        this.selecId = this.handleJSON(this.data.scopeArea);
        // this.selecId = this.handleJSON("[\"社会民生\",\"社会保障\"]");
      }

    },
    handleJSON(data, str) {
      if (data) {
        // let resultString = data.replace(/[\[\]"]/g, "");
        let resultString = data.replace(/[\[\]"]/g, "");
        if (resultString.indexOf('涉济,')>-1) {
           resultString = resultString.replace(/涉济,/g, "")
        } 
         if (resultString.indexOf(',涉济')>-1) {
           resultString = resultString.replace(/,涉济/g, "")
        }
        if (!resultString) {
          return str;
        }
        // 使用正则表达式替换逗号周围的空白字符
        //resultString = resultString.replace(/\s*,\s*/g, ",");
        if (resultString != '其他' && resultString != '未分类') {
          let list = []
          let newList = []
          if (resultString.indexOf(',') == -1) {
            list.push(resultString)
          } else {
            list = resultString.split(',')
          }
          this.domainList && this.domainList.forEach(v => {
            if (list.includes(v.name) && v.children.length == 0) {
              newList.push(v.name)
            }
            if (v.children && v.children.length > 0) {
              v.children.forEach(k => {
                if (list.includes(k.name)) {
                  newList.push(v.name + '-' + k.name)
                }
              })
            }
          })
          if (newList.length > 0) {
            return newList.join(',')
          } else {
            return resultString;
          }
        } else {
          return resultString;
        }
      } else {
        return str;
      }
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    ...mapState(useDomainStore, ["domainList"]), //监控 data 中的数据变化
  },
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() { },
};
</script>

<style scoped lang="less">
.tag {
  line-height: 20px;
  color: #333333;
  font-size: 12px;
  align-items: center;
  margin: 10px 0 10px;

  &>div {
    line-height: 20px;
    text-align: center;
  }

  .line {
    width: 1px;
    height: 20px;
    background: #999999;
    margin: 0 4px;
  }

  .area {
    min-width: 64px;
    height: 20px;
    border: 0.5px solid #5585ec;
    padding: 0 10px;
  }

  .type {
    background-color: #dee7fc;
    padding: 0 7px;
  }

  .emotion {
    padding: 0 4px;
    color: #fff;
  }

  .negative {
    background: #ffbc00;
  }

  .front {
    background: #e93d61;
  }

  .neutral {
    background: #5585ec;
  }

  .edit {
    background-color: #dee7fc;
    padding: 0 4px;
  }
}

.gs {
  position: absolute;
  background: #fff;
  color: #000;
  z-index: 1;

  div {
    width: 50px;
    text-align: center;
    height: 30px;
    line-height: 30px;
  }

  .gs-list {}

  .gs-list:hover {
    background: #e4fded;
    color: #15db55;
  }

  .gs-listOne:hover {
    background: #fdfae4;
    color: #dbca15;
  }

  .gs-listTw {}

  .gs-listTw:hover {
    background: #fde6e4;
    color: #db1515;
  }
}
</style>
