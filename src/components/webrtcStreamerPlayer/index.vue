<template>
    <div>
        <a-button @click="handleChange">切换</a-button>
        <video id="video" autoplay width="900" height="900"></video>
    </div>
</template>
<script>
import "./js/webrtcstreamer.js"
import "./js/adapter.min.js"

export default {
    props: {
        url: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            webRtcServer: null
        }
    },
    mounted() {
        //video：需要绑定的video控件ID
        //127.0.0.1:8000：启动webrtc-streamer的设备IP和端口，默认8000
        this.webRtcServer = new WebRtcStreamer('video', location.protocol + '//127.0.0.1:8000')
        //需要查看的rtsp地址
        this.webRtcServer.connect('rtsp://22293309:SP3mMaf0NQpIFZRzkuKV@*************:443/h264/ch1/main/av_stream')
        //rtsp://user:password@ip:port/h264/ch1/main/av_stream--海康
        //rtsp://user:password@ip:port/cam/realmonitor?channel=1&subtype=0--大华
    },
    beforeDestroy() {
        this.webRtcServer.disconnect()
        this.webRtcServer = null
    },
    methods: {
        handleChange() {
            this.webRtcServer.connect('rtsp://user:password@ip:port/h264/ch1/main/av_stream')
        }
    }
}
</script>
<style scoped></style>
