<template>
  <Drawer
    :closable="false"
    v-model="DrawerStatus"
    @on-close="close"
    width="700px"
  >
    <Spin v-if="loading" fix>
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
    <no-data v-if="listData.length === 0 && !loading" />
    <div class="listBox" v-if="listData.length > 0">
      <div class="hander">
        最新搜索结果
        <span class="skip cp" @click="skip"
          >点击跳转至【综合搜索】查看全部</span
        >
      </div>
      <div class="listItem flex" v-for="(item, index) in listData" :key="index">
        <div class="num">
          {{ index + 1 }}
        </div>
        <div class="content">
          <div class="title ellipsis-2 cp" @click="toDetails(item)">
            <PlatformIcon :id="item.situation" />
            {{ item.mtitle }}
          </div>
          <div class="remarks">
            <div>
              {{ item.mpublishTime }}
            </div>
            <div>
              <svg-icon icon-class="媒体类型" />
              {{
                (situation[item.situation] ? situation[item.situation] : "") +
                (situation[item.situation] ? "-" : "") +
                (item.mwebsiteName ? item.mwebsiteName : "")
              }}
            </div>
            <div v-if="item.uname">
              <Icon type="ios-contact" />
              {{ item.uname }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </Drawer>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

import PlatformIcon from "@/components/platformIcon/index.vue";

const situation = {
  0: "全部",
  30: "新闻",
  31: "新闻app",
  10: "微博",
  20: "微信公号",
  80: "小红书",
  61: "论坛",
  62: "贴吧",
  170: "知乎",
  199: "短视频",
  200: "短视频",
  230: "自媒体",
};
export default {
  name: "LatestResult",
  data() {
    // 这里存放数据
    return {
      DrawerStatus: false,
      listData: [],
      loading: false,
      situation,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { PlatformIcon },
  props: ["keyword"],
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    skip() {
      const { href } = this.$router.resolve({
        path: "/main/comprehensiveSearch/searchResults",
        query: {
          keyword: this.keyword,
        },
      });
      window.open(href, "_blank");
    },
    toDetails(d) {
      const { href } = this.$router.resolve({
        path: "/main/details",
        query: {
          msgKey: d.mkey,
          situation: d.situation,
          sourcePath: this.$route.path,
          sourceName: this.$route.name,
        },
      });
      window.open(href, "_blank");
    },
    close() {
      this.$emit("close");
    },
    getCount() {
      this.loading = true;
      let params = {
        dayNum: "99",
        emotions: null,
        orderByType: "1",
        ruleIds: [],
        searchPoisition: "1",
        situations: null,
        endTime: "2024-08-07 00:00:00",
        startTime: "2024-05-01 00:00:00",
        typeId: "1",
        pageNo: 1,
        pageSize: 10,
        keyword: this.keyword,
        // keyword: "1",
      };
      this.$http
        .post("/search/msgCount", params)
        .then((res) => {
          this.loading = false;
          if (res.body.status === 0 && res.body.data) {
            this.listData = !res.body.data.list ? [] : res.body.data.list;
            this.btnStatus = { recommendMsg: true, addMaterial: true };
          } else {
            this.listData = [];
          }
        })
        .catch((err) => {
          this.loading = false;
          this.$Message.error("服务器错误！");
        });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    keyword(d) {
      console.log(d);
      if (d) {
        this.DrawerStatus = true;
        this.getCount();
      }
    },
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style scoped lang="less">
.listBox {
  height: 100%;
  overflow-y: auto;

  .hander {
    font-size: 16px;
    margin: 0 0 20px 0;
  }

  .skip {
    text-decoration: underline;
    color: #00b1ff;
    margin-left: 20px;
  }

  .listItem {
    margin-bottom: 20px;
    font-size: 16px;

    .num {
      width: 30px;
    }

    .title {
      height: 48px;
      line-height: 24px;
    }

    .remarks {
      display: flex;
      line-height: 22px;
      color: #999;

      & > div {
        margin-right: 30px;
        display: flex;
        align-items: center;
      }

      .line {
        width: 0;
        height: 22px;
        border: 1px solid #707070;
      }
    }
  }
}
</style>
