<template>
  <div class="frame-top">
    <div class="top-left">
      <svg-icon
        icon-class="哨点-地点"
        style="width: 16px; height: 24px; margin-right: 12px"
      />
      <span v-if="textList.length != 0">
        <span v-for="(k, index) in textList" :key="index">
          <span
            :style="
              index + 1 == textList.length && textList.length != 1
                ? 'color:#5585ec;'
                : ''
            "
            >{{ k }}</span
          >
        </span>
      </span>
      <span v-else>{{ name }}</span>
    </div>
    <div class="btn">
      <slot name="slot" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    textList: {
      default: () => [],
    },
  },
  data() {
    return {
      name: "舆情监测",
    };
  },
  watch: {
    $route: {
      handler(d) {
        this.name = d.name;
      },
    },
  },
  methods: {},
  mounted() {
    this.name = this.$route.name;
  },
};
</script>
<style lang="less" scoped>
.frame-top {
  width: 100%;
  height: 70px;
  line-height: 70px;
  background: #ffffff;
  border-radius: 0 0 10px 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.16);
  padding: 0 33.22px 0 41.26px;
  display: flex;
  align-items: center;
  justify-content: left;
  position: relative;
  .top-left {
    display: flex;
    align-items: center;
    color: rgba(51, 51, 51, 0.65);
  }
  .btn {
    position: absolute;
    top: 15px;
    right: 33px;
  }
}

.addBackground {
  width: 100px;
  height: 40px;
  background: #5585ec;
  border-radius: 4px;
  color: #fff !important;
  justify-content: center;
  cursor: pointer;
}
</style>
