<template>
    <div style="position: relative;" :style="{ width: mapWidth, height: mapHeight }">
        <div id="container" style="z-index: 1;  position: absolute; left: 0px; top: 00px;"></div>
        <EditSelect :placeholder="placeholder" :selectKeyword="monitorName" :optionList="optionList" :loading="loading"
            @buttonClick="buttonClick" @selectedItem="selectedItem" @openVideo="openVideo"
            style="width: 330px; margin-right: 8px; z-index: 2;  position: absolute; left: 16px; top: 16px;">
        </EditSelect>

        <Modal ref="monitorLive" style="position: relative;" v-model="showMonitorFlag" :mask-closable="false" width="1200px"
      @on-cancel="closeMonitorLive">
      <img style="z-index: 1;" width="1200px" height="715px" src="~@/assets/img/雪亮摄像头背景框.png">
      <Button-group style="z-index: 2;  position: absolute; left: 1050px; bottom: 20px;">
                <Button type="ghost" @click='clickPlayType' :class="{ 'chooseButton': videoType == 0 }">直播</Button>
                <Button type="ghost" @click='clickPlayType1' :class="{ 'chooseButton': videoType == 1 }">回放</Button>
      </Button-group>
      <HaikangPlayer v-if="showMonitorFlag" :code="videoId" index="videoHome" :type="videoType"
        ref="webPlugin" videoWidth="1180px" videoHeight="610px" :key="itemKey"
        style="z-index: 2;  position: absolute; left: 10px; top: 55px;">
      </HaikangPlayer>
      <div slot="footer"></div>
    </Modal>
    </div>
</template>

<script>

import EditSelect from '@/components/editSelect/index.vue';
import HaikangPlayer from '@/components/haikangPlayer/index.vue';
export default {
    name: 'baiduMap',
    components: { EditSelect, HaikangPlayer },
    props: {
        mapWidth: {
            type: String,
            required: true
        },
        mapHeight: {
            type: String,
            required: true
        },
        selected: {
            type: [String, Number]
        },
        placeholder: {
            type: String,
            default: ''
        },
        loading: {
            type: Boolean,
            default: false
        },
        keyName: {
            type: String
        },
        monitorName: {
            type: String
        },
        msgKey: {
            type: String
        },
        optionList: {
            type: Array,
            default: () => {
                return []
            }
        },
        monitorList: {
            type: Array,
            default: () => {
                return []
            }
        },
    },
    data() {
        return {
            // 地图实例
            map: null,
            // 点实例
            mk: null,
            monitorLoading: false,
            monitorOptionList: [],
            clickMarker: {},
            videoId: "",
            itemKey: 0,
            showMonitorFlag: false,
            markerClusterer: null,
            videoType: 0
        }
    },
    created() {
        // 加载地图
        this.$nextTick(() => {
            this.initMap()
        })
    },
    watch: {
        optionList: {
            handler(val) {

                var Point = new BMap.Point(Number(val[0].longitude) + 0.01263, Number(val[0].latitude) + 0.0068);

                // 平移中心点
                // this.map.panTo(Point);
                this.map.centerAndZoom(Point, 19);

                // Point = new BMap.Point(117.30428, 36.69012);
                // this.mk = new BMap.Marker(Point, that.map.getZoom());
                var bs = this.map.getBounds();   //获取可视区域
                var bssw = bs.getSouthWest();   //可视区域左下角
                var bsne = bs.getNorthEast();   //可视区域右上角
                console.log("当前地图可视范围是：" + bssw.lng + "," + bssw.lat + "到" + bsne.lng + "," + bsne.lat);
                console.log("当前地图中心点：" + this.map.getCenter().lng + "," + this.map.getCenter().lat);

                let visualArea = {
                    left: Number(bssw.lng) - 0.01263,
                    right: Number(bsne.lng) - 0.01263,
                    top: Number(bsne.lat) - 0.0068,
                    bottom: Number(bssw.lat) - 0.0068
                }
                this.$emit("visualList", visualArea);
            },
        },
        monitorList: {
            handler(val) {
                // 清空点标注
                this.map.clearOverlays();

                if (this.markerClusterer != null) {
                    this.markerClusterer.clearMarkers();
                }

                let markers = [];
                let zoom = this.map.getZoom();
                let that = this;
                // 添加标注点
                val.forEach((monitor, index) => {
                    //let Point = new BMap.Point(Number(monitor.longitude) + 0.01263, Number(monitor.latitude) + 0.0068);
                    let Point = new BMap.Point(Number(monitor.longitude) + 0.01263, Number(monitor.latitude) + 0.0068);
                    monitor.value = monitor.cameraIndexCode;
                    monitor.label = monitor.name;
                    that.monitorOptionList.push(monitor);

                    var myIcon = new BMap.Icon(require("../../assets/img/摄像头1.png"), new BMap.Size(32, 32),
                        {
                            imageSize: new BMap.Size(32, 32)
                        });
                    let marker = new BMap.Marker(Point, { enableDragging: false, icon: myIcon });

                    if (zoom >= 16 && zoom < 19) {
                        markers.push(marker);
                    }

                    if (zoom >= 19) {
                        that.map.addOverlay(marker)

                        if (that.clickMarker.cameraIndexCode == monitor.cameraIndexCode) {
                            let monitorUrl = require("../../assets/img/摄像头.png");

                            let html = '<div>'
                            html += '<div style="margin-bottom: 8px"></div>'
                            html += '<div style="margin-bottom: 8px">'
                            html += `<p style="font-size: 16px; font-weight: bold;">${monitor.name}</p>`
                            html += '</div>'
                            html += `<div style="border-bottom: 1px solid black; margin-bottom: 8px">  </div>`
                            html += `<div><img style="cursor:pointer;" width="32" height="32" src="${monitorUrl}" id="${monitor.cameraIndexCode}"/><span style="font-size: 16px; color: #5092e2; margin-left: 100px; cursor:pointer;" id="SAVE${monitor.cameraIndexCode}">保存事发地</span></div>`
                            html += '</div>'

                            const infoWindow = new BMap.InfoWindow(html);
                            marker.openInfoWindow(infoWindow, Point);

                            let that2 = that;
                            setTimeout(() => {
                                console.log("monitor.cameraIndexCode:" + monitor.cameraIndexCode);
                                document
                                    .getElementById(monitor.cameraIndexCode)
                                    .addEventListener("click", function () {

                                        that2.openVideo(monitor.cameraIndexCode);
                                    });

                                document
                                    .getElementById("SAVE" + monitor.cameraIndexCode)
                                    .addEventListener("click", function () {

                                        that2.saveAddress(monitor.name);
                                    });
                            }, 500);
                        }
                        marker.addEventListener("click", function () {  //mouseover
                            let monitorUrl = require("../../assets/img/摄像头.png");

                            let html = '<div>'
                            html += '<div style="margin-bottom: 8px"></div>'
                            html += '<div style="margin-bottom: 8px">'
                            html += `<p style="font-size: 16px; font-weight: bold;">${monitor.name}</p>`
                            html += '</div>'
                            html += `<div style="border-bottom: 1px solid black; margin-bottom: 8px">  </div>`
                            html += `<div><img style="cursor:pointer;" width="32" height="32" src="${monitorUrl}" id="${monitor.cameraIndexCode}"/><span style="font-size: 16px; color: #5092e2; margin-left: 100px; cursor:pointer;" id="SAVE${monitor.cameraIndexCode}">保存事发地</span></div>`
                            html += '</div>'

                            const infoWindow = new BMap.InfoWindow(html);
                            marker.openInfoWindow(infoWindow, Point);

                            let that2 = that;
                            setTimeout(() => {
                                console.log("monitor.cameraIndexCode:" + monitor.cameraIndexCode);
                                document
                                    .getElementById(monitor.cameraIndexCode)
                                    .addEventListener("click", function () {

                                        that2.openVideo(monitor.cameraIndexCode);
                                    });

                                document
                                    .getElementById("SAVE" + monitor.cameraIndexCode)
                                    .addEventListener("click", function () {

                                        that2.saveAddress(monitor.name);
                                    });
                            }, 500);
                        });
                    }
                });


                //最简单的用法，生成一个marker数组，然后调用markerClusterer类即可。
                if (zoom >= 16 && zoom < 19) {
                    that.markerClusterer = new BMapLib.MarkerClusterer(this.map, {
                        markers: markers,
                        gridSize: 600 // 网格大小
                    });

                    //that.markerClusterer.clearMarkers();

                }
            },
        },
    },
    methods: {
        // 获取地图
        initMap() {
            // 内网使用-地图对象-在public/index.html引入文件
            let BMap = window.BMap
            console.log('window.BMap', window.BMap)
            // this指向
            const that = this
            // 创建Map实例
            this.map = new BMap.Map('container')
            // 地图中心点-经纬度决定我们加载哪里的地图
            var Point = new BMap.Point(117.30428, 36.69012)
            // 初始化地图中心点和放大级别
            this.map.centerAndZoom(Point, 19)

            // 限制地图放大等级-为什么限制
            // 1.因为内网时我们访问不到公网百度地图数据资源
            // 2.同时这里地图放大等级也对应着我们下载的瓦片图资源
            this.map.setMinZoom(10)
            this.map.setMaxZoom(19)
            // 4、添加（右上角）平移缩放控件
            this.map.addControl(
                new BMap.NavigationControl({
                    anchor: BMAP_ANCHOR_TOP_RIGHT,
                    type: BMAP_NAVIGATION_CONTROL_SMALL
                })
            )
            //缩略地图控件。
            this.overView = new BMap.OverviewMapControl({ isOpen: true });
            //添加控件
            this.map.addControl(this.overView);

            this.map.addControl(new BMap.ScaleControl());

            //开启鼠标滚轮缩放
            this.map.enableScrollWheelZoom(true)
            // 4、添加（右上角）平移缩放控件
            this.map.addControl(
                new BMap.NavigationControl({
                    anchor: BMAP_ANCHOR_TOP_RIGHT,
                    type: BMAP_NAVIGATION_CONTROL_SMALL
                })
            )

            // 添加中心标注点
            // this.mk = new BMap.Marker(Point, { enableDragging: true })
            // this.map.addOverlay(this.mk)
            // 拖拽点标注事件
            // this.mk.addEventListener('dragend', function (e) {
            //     console.log('执行点位拖拽', e)
            //     //that.getAddrByPoint(e.point)
            // })

            // 地图标注点击事件
            // this.map.addEventListener('click', function (e) {
            //     console.log('点击事件', e)

            //     // 设置地图中心点
            //     that.map.panTo(e.point)
            //     //that.getAddrByPoint(e.point)
            //     console.log("this.map.getZoom():" + that.map.getZoom());

            //     var center = that.map.getCenter();
            //     console.log("地图中心点变更为：" + center.lng + ", " + center.lat);
            // })

            // 地图标注点击事件
            this.map.addEventListener('zoomend', function (e) {

                console.log("this.map.getZoom():" + that.map.getZoom());
                if (that.map.getZoom() >= 16) {
                    // Point = new BMap.Point(117.30428, 36.69012);
                    // this.mk = new BMap.Marker(Point, that.map.getZoom());
                    var bs = that.map.getBounds();   //获取可视区域
                    var bssw = bs.getSouthWest();   //可视区域左下角
                    var bsne = bs.getNorthEast();   //可视区域右上角
                    console.log("当前地图可视范围是：" + bssw.lng + "," + bssw.lat + "到" + bsne.lng + "," + bsne.lat);
                    console.log("当前地图中心点：" + that.map.getCenter().lng + "," + that.map.getCenter().lat);

                    let visualArea = {
                        left: Number(bssw.lng) - 0.01263,
                        right: Number(bsne.lng) - 0.01263,
                        top: Number(bsne.lat) - 0.0068,
                        bottom: Number(bssw.lat) - 0.0068
                    }
                    that.$emit("visualList", visualArea);
                }
                if (that.map.getZoom() < 16) {
                    that.map.clearOverlays();
                    that.markerClusterer.clearMarkers()
                }
            })

            // 拖拽
            this.map.addEventListener("dragend", function () {


                if (that.map.getZoom() >= 16) {
                    // Point = new BMap.Point(117.30428, 36.69012);
                    // this.mk = new BMap.Marker(Point, that.map.getZoom());
                    var bs = that.map.getBounds();   //获取可视区域
                    var bssw = bs.getSouthWest();   //可视区域左下角
                    var bsne = bs.getNorthEast();   //可视区域右上角
                    console.log("当前地图可视范围是：" + bssw.lng + "," + bssw.lat + "到" + bsne.lng + "," + bsne.lat);
                    console.log("当前地图中心点：" + that.map.getCenter().lng + "," + that.map.getCenter().lat);

                    let visualArea = {
                        left: Number(bssw.lng) - 0.01263,
                        right: Number(bsne.lng) - 0.01263,
                        top: Number(bsne.lat) - 0.0068,
                        bottom: Number(bssw.lat) - 0.0068
                    }
                    that.$emit("visualList", visualArea);
                }
            })

        },
        // 逆地址解析数据
        getAddrByPoint(Point) {
            console.log('执行解析', Point)
            // 内网使用-地图对象-在public/index.html引入文件
            let BMap = window.BMap
            console.log('new BMap.Geocoder()', new BMap.Geocoder())
            // 这个api是不执行的-因为逆地址解析是需要调用地图api的-只有在public/index.html正常引入百度地图时才会生效
            var geoc = new BMap.Geocoder()
            geoc.getLocation(Point, rs => {
                console.log('点击地址-获取信息', rs)
            })
            console.log('执行解析完毕')
        },
        buttonClick(keyWord) {
            if (keyWord) {
                this.$emit("buttonClick", keyWord);
            }
        },
        selectedItem(item) {

            var Point = new BMap.Point(Number(item.longitude) + 0.01263, Number(item.latitude) + 0.0068);
            //this.map.panTo(Point);
            this.map.centerAndZoom(Point, 19);

            // Point = new BMap.Point(117.30428, 36.69012);
            // this.mk = new BMap.Marker(Point, that.map.getZoom());
            var bs = this.map.getBounds();   //获取可视区域
            var bssw = bs.getSouthWest();   //可视区域左下角
            var bsne = bs.getNorthEast();   //可视区域右上角
            console.log("当前地图可视范围是：" + bssw.lng + "," + bssw.lat + "到" + bsne.lng + "," + bsne.lat);
            console.log("当前地图中心点：" + this.map.getCenter().lng + "," + this.map.getCenter().lat);

            let visualArea = {
                left: Number(bssw.lng) - 0.01263,
                right: Number(bsne.lng) - 0.01263,
                top: Number(bsne.lat) - 0.0068,
                bottom: Number(bssw.lat) - 0.0068
            }

            this.clickMarker = item;
            this.$emit("visualList", visualArea);
        },
        closeMonitorLive() {
            this.showMonitorFlag = false;
        },
        openVideo(videoId) {
            this.videoId = videoId;
            this.videoType = 0;
            this.showMonitorFlag = true;
            this.itemKey++;
        },
        saveAddress(monitorName) {
            let params = {
                msgKey: this.msgKey, //提示单主键
                isMonitor: 1, //1 已保存事发地
                monitorName: monitorName //事发地
            };
            this.$http
                .post("/recommend/updateMsgIsMonitor", params, { emulateJSON: true })
                .then((res) => {
                    if (res.body.status === 0) {
                        this.$Message.success("保存事发地成功");
                    }
                });
            this.showSaveAddressButton = false;
        },
        clickPlayType() {
            this.videoType = 0;
            this.itemKey++;
        },
        clickPlayType1() {
            this.videoType = 1;
            this.itemKey++;
        },
    }
}
// 须知
// 1.内网离线情况下我们访问不了百度数据资源，所有只有把数据资源下载在本地启动服务才可以解决这个问题
// 2.确实可以满足基本的需求
// 3.地图放大，缩小，控件，定位
// 4.加载那里的地图我们首先要下载地图瓦片图，然后地图中心点定位在哪里就会加载的地图
// 5.地图的放大等级我们要控制起来，因为是下载的瓦片图要对应
// 6.瓦片图下载等级最好下载，11-15，刚好铺满全屏，再多就下载很慢
// 7.开发时候我们用npm下载serve，把地图资源从本机运行起来（直接serve）
// 8.所有地址最好写127.0.0.1和localhost方便测试（因为你后续直接断开网线测试时候，你就只能访问自己）
// 瓦片图加载报错-404解决
// 1.瓦片图，就是把一个地图区域根据放大等级分割成等额大小图片进行加载
// 1.首先确定地图中心点经纬度和下载地图瓦片图是否是一致的
// 2.瓦片图下载等级和代码地图限制放大等级是否一样（比如地图放大19级，但是下载地图没有19级图片也会报错）
// 3.用serve启动好本地图片资源后-查看map_load.js文件（一般默认不用改）
// 4..把404图片路径复制到本机浏览器看是否能访问
// 优点
// 1.引入项目比较轻便-确实在内网情况下可以使用(不能访问外网情况下-离线)
// 缺点
// 1.鼠标滚动的时候，会调用百度的api报错，但不影响使用
// 2.百度api会失效，比如逆地址解析，没有建立百度资源连接带身份（就是没有在public/index-正常引入百度地图）
// 3.如果单纯地图展示看，定位-可以，要使用api不满足-目前还没找到解决方案
</script>

<style scoped>
#container {
    width: 100%;
    height: 100%;
}

::v-deep .anchorBL img {
    display: none;
}

::v-deep .BMap_cpyCtrl span {
    display: none !important;
}

::v-deep .ivu-modal {
    box-shadow: none !important;
    background-color: rgba(0, 0, 0, 0);
    /* 设置背景透明 */
}

::v-deep .ivu-modal-content {
    background-color: rgba(0, 0, 0, 0);
    /* 设置内容区域透明 */
}

::v-deep .ivu-modal-body {
    color: rgba(0, 0, 0, 0);
    padding: 0px !important;
}

::v-deep .ivu-modal-footer {
    padding: 0px !important;
}

::v-deep .ivu-modal-mask {
    background-color: rgba(0, 0, 0, 0.5);
}

::v-deep .ivu-modal-close .ivu-icon-ios-close {
  font-size: 45px;
  font-weight: 500;
  color: #e6c81c;
}

.chooseButton {
    color: #5092e2;
}
</style>
