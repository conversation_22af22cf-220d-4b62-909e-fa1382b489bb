<template>
  <div class="index">
    <div class="row flex">
      <div class="label">原标题：</div>
      <div class="value">
        <Input v-model="OriginalText" type="textarea" :rows="10" disabled />
      </div>
    </div>
    <div class="row flex">
      <div class="label">新标题：</div>
      <div class="value">
        <Input v-model="value" type="textarea" :rows="10" />
      </div>
    </div>
    <div class="foot">
      <span class="foots" @click="submit()">确定</span>
      <span
        class="foots"
        style="margin-left: 70px; background: #999999"
        @click="close()"
        >关闭</span
      >
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
  data() {
    // 这里存放数据
    return {
      OriginalText: "",
      value: "",
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    data: {},
    that: {},
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.getOriginalText();
  },
  // 方法集合
  methods: {
    submit() {
      let params = {
        situation: this.data.situation || this.data.msgSiution,
        mkey: this.data.mkey || this.data.msgId, //消息mkey
        mtitle: this.value, //摘要
      };
      this.that.$http
        .post("/search/updateTitle", params, {
          emulateJSON: true,
        })
        .then((res) => {
          console.log(res);
          if (res.body.status === 0) {
            this.$Message.success("保存成功");
            if (this.data.situation == 10) {
              this.data.mcontent = this.value;
            }else {
               this.data.mtitle = this.value;
            }
            this.$emit("close", this.value);
            
          } else {
            this.$Message.error("保存失败");
          }
        });
    },
    close() {
      this.$emit("close", this.value);
    },
    getOriginalText() {
      let params = { msgKey: this.data.mkey, situation: this.data.situation };
      this.that.$http.get("/search/msgDetail", { params }).then((res) => {
        if (res.body.status == 0) {
            if (this.data.situation == 10) {
                this.OriginalText = res.body.data.mcontent;
            }else {
               this.OriginalText = res.body.data.mtitle;
            }
        }else {
           if (this.data.situation == 10) {
                this.OriginalText = this.data.mcontent;
            }else {
               this.OriginalText = this.data.mtitle;
            }
        }
       
      });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.index {
  width: 1000px;
  padding: 20px;
  .row {
    margin: 10px;
    color: #000;
    font-size: 16px;

    .label {
      width: 66px;
      line-height: 200px;
      font-weight: 600;
    }
    .value {
      flex: 1;
      /deep/.ivu-input-disabled {
        background-color: #f8f8f8;
        color: #8e8e8e;
      }
      /deep/.ivu-input {
        font-size: 16px;
      }
    }
  }
  .foot {
    text-align: center;
    margin-bottom: 18px;
    .foots {
      display: inline-block;
      width: 80px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background: #5585ec;
      border-radius: 4px;
      font-family: PingFang SC;
      font-weight: 600;
      color: #ffffff;
      font-size: 14px;
      cursor: pointer;
    }
  }
}
</style>
