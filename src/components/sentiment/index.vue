<template>
  <div
    :class="[
      'emotion',
      data.msentiment === 1
        ? 'negative'
        : data.msentiment === 0
        ? 'neutral'
        : 'front',
    ]"
    @click="handelNewModal"
  >
    {{ data.msentiment === 1 ? "正" : data.msentiment === 0 ? "中" : "负" }}
    <div class="gs" v-if="modalNew">
      <div class="gs-list" @click="handelMsent(1)">正面</div>
      <div class="gs-listOne" @click="handelMsent(0)">中性</div>
      <div class="gs-listTw" @click="handelMsent(-1)">负面</div>
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
  data() {
    // 这里存放数据
    return {
      modalNew: false, //下拉框展示
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    data: {},
    changeBack: {
      default: null
    },
    isShow: {
      default: true
    }
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    // 展示程度
    handelNewModal() {
      if(this.$route.query.isQx == '1'){
        return;
      }

      if(!this.isShow){
        return;
      }
      this.modalNew = !this.modalNew;
      this.modal = false;
    },
    handelMsent(id) {
      if(this.changeBack){
        this.changeBack(id)
        return
      }
      this.handleSubmit(3, null, id);
    },
    handleSubmit(type, val, id) {
      let url = "/search/updateLabel";
      let params = {
        situation: this.data.situation,
        mkey: this.data.mkey,
      };
      if (type == 1) {
        if (val.length == 0) {
          params.marea = "其他";
        } else {
          if (val.indexOf("其他") > -1) {
            val.splice(val.indexOf("其他"), 1);
          }
          params.marea = val.toString();
        }
      } else if (type == 2) {
        if (val.length == 0) {
          params.scopeArea = "未分类";
        } else {
          if (val.indexOf("未分类") > -1) {
            val.splice(val.indexOf("未分类"), 1);
          }
          params.scopeArea = val.toString();
        }
      } else if (type == 3) {
        params.msentiment = id;
      }
      this.$http.post(url, params, { emulateJSON: true }).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          if (type == 1) {
            this.data.marea =
              val.length != 0 ? JSON.stringify(val) : '["其他"]';
          } else if (type == 2) {
            this.data.scopeArea =
              val.length != 0 ? JSON.stringify(val) : '["未分类"]';
          } else if (type == 3) {
            this.data.msentiment = id;
          }
          this.close();
        } else {
          this.$Message.error(result.message);
        }
      });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.emotion {
  padding: 0 4px;
  color: #fff;
}

.negative {
  background: #ffbc00;
}

.front {
  background: #e93d61;
}

.neutral {
  background: #5585ec;
}

.edit {
  background-color: #dee7fc;
  padding: 0 4px;
}
.gs {
  position: absolute;
  background: #fff;
  color: #000;
  z-index: 1;
  div {
    width: 50px;
    text-align: center;
    height: 30px;
    line-height: 30px;
  }
  .gs-list {
  }

  .gs-list:hover {
    background: #e4fded;
    color: #15db55;
  }

  .gs-listOne:hover {
    background: #fdfae4;
    color: #dbca15;
  }

  .gs-listTw {
  }

  .gs-listTw:hover {
    background: #fde6e4;
    color: #db1515;
  }
}
</style>
