<template>
  <div class="videoPlayer">
    <!-- 模糊背景视频 -->
    <video
      ref="backgroundVideo"
      class="backgroundVideo"
      preload="metadata"
    ></video>
    <video ref="videoPlayer" class="video-js" preload="metadata"></video>
  </div>
</template>

<script>
import videojs from "video.js";

export default {
  name: "VideoPlayer",
  props: {
    options: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      videoPlayer: null,
      backgroundVideo: null,
    };
  },
  // 方法集合
  methods: {
    player() {
      if (this.videoPlayer) {
        this.videoPlayer.play();
        this.backgroundVideo.play();
      }
    },
    toDetails() {
      console.log("详情页");
      this.$emit("toDetails");
    },
    showControls() {
      const videoContainer = this.$refs.videoPlayer;
      videoContainer.classList.add("vjs-video-hover");
    },
    hideControls() {
      const videoContainer = this.$refs.videoPlayer;
      console.log("1231");
      videoContainer.classList.remove("vjs-video-hover");
    },
  },
  mounted() {
    this.videoPlayer = videojs(this.$refs.videoPlayer, this.options);
    this.backgroundVideo = videojs(this.$refs.backgroundVideo, this.options);
    this.videoPlayer.on("pause", (e) => {
      this.playState = false;
      this.backgroundVideo.pause();
    });
    // 监听播放错误事件
    this.videoPlayer.on("error", () => {
      console.log("视频播放错误: ", this.videoPlayer.error());
    });
  },
  beforeDestroy() {
    console.log("触发时间");
    // 移除暂停事件的监听器
    if (this.videoPlayer && this.handlePause) {
      this.videoPlayer.off("pause", this.handlePause);
    }
    // 销毁 Video.js 播放器实例
    if (this.player) {
      this.videoPlayer.dispose();
    }
  },
};
</script>
<style lang="less" scoped>
.videoPlayer {
  height: 100%;
  width: 100%;
  position: relative;
  .backgroundVideo {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150%; /* 放大背景视频 */
    height: 150%;
    object-fit: cover;
    transform: translate(-50%, -50%);
    filter: blur(20px); /* 高斯模糊效果 */
    z-index: 0; /* 让背景视频处于底部 */
  }
  .masking {
    position: absolute;
    top: 10%;
    bottom: 10%;
    right: 0;
    left: 0;
    z-index: 99;
  }

  /* 隐藏控件 */

  /deep/ .vjs-control-bar {
    opacity: 0;
    transition: opacity 0.3s;
  }

  /* 鼠标移入时显示控件 */

  /deep/ .vjs-video-hover,
  /deep/ .vjs-control-bar {
    opacity: 1;
  }

  .video-js {
    height: 100%;
    width: 100%;
    object-fit: cover;
    z-index: 1; /* 确保视频在其他内容的下方 */
    background-color: rgba(1, 1, 1, 0);
  }

  video {
    background-color: rgba(1, 1, 1, 0);
  }
}
/deep/.vjs-time-tooltip,
/deep/.vjs-volume-tooltip {
  width: 50px;
}

</style>
