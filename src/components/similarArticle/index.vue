<template>
  <div class="similarArticle">
    <div class="title">相似文章（{{ data.topicNum }}）</div>
    <div class="listBox">
      <div class="item flex" v-for="(i, index) in listData" :key="i.id">
        <div class="label">{{ index + 1 }}</div>
        <div class="content">
          <div class="text ellipsis-2">
            {{ i.mtitle }}
          </div>
          <div class="remark">
            <span> 来源：{{ i.uname || i.mwebsiteName }} </span>
            <span>
              时间：{{ moment(i.mpublishTime).format("YYYY-MM-DD HH:mm:ss") }}
            </span>
          </div>
        </div>
      </div>
      <div class="more cp" @click="more" v-if="data.topicNum > listData.length">
        点击查看更多
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

import moment from "moment";

export default {
  data() {
    // 这里存放数据
    return {
      listData: [],
      pageNo: 1,
      pageSize: 20,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    data: {},
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    more() {
      this.pageNo++;
      this.getDataList();
    },
    getDataList() {
      let params = {
        id: this.data.id,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
      };
      this.$http.get("/recommend/topicList", { params }).then((res) => {
        console.log(res);
        let data = res.body.data.data;
        this.listData = this.listData.concat(data);
      });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    moment() {
      return moment;
    },
  },
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getDataList();
  },
};
</script>

<style scoped lang="less">
.similarArticle {
  width: 718px;

  .more {
    color: #5585ec;
    text-align: center;
  }

  .title {
    font-size: 18px;
    font-weight: 600;
  }

  .listBox {
    height: calc(~"100vh - 80px");
    overflow-y: auto;
    margin-top: 20px;

    .item {
      .label {
        width: 30px;
        font-size: 16px;
      }

      .content {
        flex: 1;

        .text {
          font-size: 16px;
          line-height: 22px;
        }

        .remark {
          color: #999;
          line-height: 24px;
          font-size: 14px;
          margin-top: 10px;
        }
      }
    }
  }
}
</style>
