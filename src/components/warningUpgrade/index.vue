<template>
  <div class="warningUpgrade" ref="warningUpgrade">
    <Poptip width="350" v-model="visible" @on-ok="ok">
      <div class="api" slot="content">
        <div class="content">
          <Icon
            :style="{ color: warningUpgradeId === 2 ? '#ff8000' : '#f00' }"
            type="md-help-circle"
          />
          是否将“{{ info.msgTitle }}”预警级别改为
          <span :style="{ color: warningUpgradeId === 2 ? '#ff8000' : '#f00' }"
            >{{ warningUpgradeId === 2 ? "橙色" : "红色" }}预警</span
          >
          ？
        </div>
        <div class="btnBox">
          <Button size="small" type="text" @click="visible = false"
            >取消</Button
          >
          <Button size="small" type="primary" @click="submit">确定</Button>
        </div>
      </div>
    </Poptip>

    <div class="yjBtn" @click="openList" v-if="account.includes(currentAccount)">
      <svg-icon icon-class="预警升级" />
      预警提级
    </div>
    <div class="dropDown" v-if="dropDownStatus">
      <div
        class="item cp"
        @click.stop="openPoptip(2)"
        v-show="info.noticeType == 1"
      >
        橙色预警
      </div>
      <div class="item cp" @click.stop="openPoptip(3)">红色预警</div>
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
  data() {
    // 这里存放数据
    return {
      dropDownStatus: false,
      visible: false,
      warningUpgradeId: 1,
      account: ["chensu3", "chenzhe", "qianhongguo1", "test","xupengxiang"],
      currentAccount: localStorage.getItem("userAccount"),
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    info: {},
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    submit() {
      let params = {
        promptMsgId: this.info.promptMsgId,
        noticeType: this.warningUpgradeId,
      };
      this.$http
        .post("/prompt/updateNoticeType", params, { emulateJSON: true })
        .then((res) => {
          console.log(res);
          if (res.body.status === 0) {
            this.$Message.success("操作成功！");
          }
          this.$emit("ok");
        });
    },
    openPoptip(id) {
      this.visible = true;
      this.warningUpgradeId = id;
      this.dropDownStatus = false;
    },
    ok() {},
    openList() {
      this.dropDownStatus = true;
      this.addEvent();
    },
    addEvent() {
      document.addEventListener("click", this.handleOutsideClick);
    },
    handleOutsideClick(event) {
      const element = this.$refs.warningUpgrade;
      // 判断点击是否在元素内部
      if (!element.contains(event.target)) {
        console.log("点击了外边");
        this.dropDownStatus = false;
        document.removeEventListener("click", this.handleOutsideClick); // 清除监听器
        // this.$emit("outside-click"); // 触发外部点击事件
      } else {
        console.log("点击了内部");
      }
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.warningUpgrade {
  margin: 0 5px;
  width: 100px;
  position: relative;
  //   /deep/.ivu-poptip-popper {
  //     top: -100px !important;
  //     left: -150px !important;
  //   }
  .content {
    margin-top: 10px;
    width: 300px;
    text-wrap: wrap;
  }
  .btnBox {
    margin-top: 10px;
    display: flex;
    justify-content: right;
  }

  .yjBtn {
    width: 100px;
    background: #e6f7ff;
    border: 1px solid #5585ec;
    border-radius: 4px;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    text-align: center;
    height: 24px;
    position: absolute;
    top: 0px;
    font-weight: normal;
    color: #333;
  }
  .dropDown {
    margin-left: 5px;
    height: 40px;
    width: 100px;
    background-color: #fff;
    .item {
      height: 20px;
      text-align: center;
      font-size: 14px;
      &:nth-child(1) {
        &:hover {
          color: #ff8000;
          background-color: rgba(255, 128, 0, 0.2);
        }
      }
      &:nth-child(2) {
        &:hover {
          color: #ff0000;
          background-color: rgba(255, 0, 0, 0.2);
        }
      }
    }
  }
}
</style>
