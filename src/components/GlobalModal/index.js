import Vue from "vue";
import GlobalModal from "./GlobalModal.vue";

const ModalConstructor = Vue.extend(GlobalModal);

let instances = []; // 存储所有弹窗实例
let seed = 1; // 弹窗唯一标识的种子

// 初始化弹窗实例
const initInstance = ({
  component,
  componentProps,
  componentEvents,
  title,
  x,
  y,
  showHeader,
  overlayEnabled,
  isOverlay,
}) => {
  const id = `modal_${seed++}`;
  const instance = new ModalConstructor({
    el: document.createElement("div"),
    propsData: {
      component,
      componentProps,
      componentEvents,
      title,
      x,
      y,
      showHeader,
      overlayEnabled,
      isOverlay,
      zIndex: 1000 + instances.length,
    },
  });
  instance.id = id; // 为实例添加唯一标识
  document.body.appendChild(instance.$el); // 将实例添加到DOM
  instance.open(); // 打开弹窗
  instance.$on("destroy", () => {
    removeInstance(instance); // 当销毁事件触发时移除实例
  });
  instances.push(instance); // 将实例添加到存储数组
  return id; // 返回实例的唯一标识
};

// 移除弹窗实例
const removeInstance = (instance) => {
  const index = instances.findIndex((inst) => inst === instance);
  if (index !== -1) {
    instances.splice(index, 1); // 从存储数组中移除实例
    document.body.removeChild(instance.$el); // 从DOM中移除实例
  }
};

// 显示弹窗
const showModal = (options) => {
  return initInstance(options);
};

// 关闭弹窗
const hideModal = (id = null) => {
  if (id === null) {
    hideAllModals(); // 不传id时关闭所有弹窗
  } else {
    const instance = instances.find((inst) => inst.id === id);
    if (instance) {
      instance.close();
    }
  }
};

// 关闭所有弹窗
const hideAllModals = () => {
  instances.forEach((instance) => {
    instance.close();
  });
  instances = []; // 清空存储数组
};

// 注册到Vue原型上，方便全局调用
Vue.prototype.$modal = {
  show: showModal,
  hide: hideModal,
  hideAll: hideAllModals,
};
