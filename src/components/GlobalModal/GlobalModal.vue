<template>
  <!-- 弹窗外部的遮罩层，点击此遮罩层可以关闭弹窗 -->
  <div
    v-if="visible"
    :class="['modal-overlay', { 'modal-overlay-transparent': !overlayEnabled }]"
    @click="handleOverlayClick"
    :style="{zIndex: zIndex}"
  >
    <!-- 弹窗容器，显示在遮罩层之上 -->
    <div class="modal-container" :style="modalStyle">
      <!-- 可选的弹窗头部，包含标题和关闭按钮 -->
      <!-- <div v-if="showHeader" class="modal-header">
          <span class="modal-title">{{ title }}</span>
          <button class="modal-close" @click="close">关闭</button>
        </div> -->
      <div
        v-if="showHeader"
        class="modal-header"
        style="height: 80px; line-height: 80px; text-align: center"
      >
        <span class="modal-xian inBlock"></span>
        <span class="yuan inBlock" style="margin: 0px 98px 0px 3px"></span>
        <span style="font-weight: 600; color: #ffffff; font-size: 24px">{{
          title
        }}</span>
        <span class="yuan inBlock" style="margin: 0px 3px 0px 98px"></span>
        <span
          class="modal-xian inBlock"
          style="
            background: linear-gradient(
              90deg,
              #ebedf8 0%,
              rgba(255, 255, 255, 0) 100%
            );
          "
        ></span>
      </div>
      <!-- 弹窗内容区，通过动态组件加载 -->
      <div class="modal-content">
        <component
          :is="component"
          v-bind="componentProps"
          v-on="componentEvents"
          ref="componentName"
        ></component>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "GlobalModal",
  props: {
    component: {
      type: [Object, Function], // 要显示的组件，必须的
      required: true,
    },
    componentProps: {
      type: Object, // 要传递给组件的props
      default: () => ({}),
    },
    componentEvents: {
      type: Object, // 要传递给组件的事件处理程序
      default: () => ({}),
    },
    title: {
      type: String, // 弹窗标题，可选
      default: "",
    },
    x: {
      type: Number, // 弹窗x坐标，可选
      default: null,
    },
    y: {
      type: Number, // 弹窗y坐标，可选
      default: null,
    },
    showHeader: {
      type: Boolean, // 是否显示头部，默认显示
      default: true,
    },
    zIndex: {
      type: Number, // 弹窗层级
      default: 1000,
    },
    overlayEnabled: {
      type: Boolean, // 是否显示蒙版
      default: true,
    },
    isOverlay: {
      type: Boolean, 
      default: false
    }
  },
  data() {
    return {
      visible: false, // 控制弹窗的显示和隐藏
    };
  },
  computed: {
    modalStyle() {
      // 动态设置弹窗样式
      let style = {
        zIndex: this.zIndex,
        top: this.y === null ? '50%' : `${this.y}px`,
        left: this.x === null ? '50%' : `${this.x}px`,
        transform: this.x === null && this.y === null ? 'translate(-50%, -50%)' : ''
      };
      return style;
    }
  },
  methods: {
    open() {
      this.visible = true; // 打开弹窗
    },
    close() {
      this.$nextTick(() => {
        const overlay = this.$el.querySelector(".modal-overlay");
        const container = this.$el.querySelector(".modal-container");

        if (overlay && container) {
          overlay.style.animation = "fadeOut 0.3s";
          container.style.animation = "zoomOut 0.3s";

          overlay.addEventListener(
            "animationend",
            () => {
              this.visible = false;
              this.$emit("destroy");
            },
            { once: true }
          );
        } else {
          this.visible = false;
          this.$emit("destroy");
        }
      });
    },
    handleOverlayClick(event) {
      // 如果点击的是遮罩层本身，关闭弹窗
      if (event.target === event.currentTarget) {
        this.close();
        if(this.isOverlay){
          this.$refs.componentName.close && this.$refs.componentName.close()
        }
      }
    },
  },
};
</script>

<style>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s; /* 打开动画 */
}
.modal-overlay-transparent {
  background: transparent; /* 透明背景 */
}
.modal-container {
  background: #fff;
  border-radius: 5px;
  /* max-width: 500px; */
  transform: scale(0.7);
  animation: zoomIn 0.3s forwards; /* 打开动画 */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.7); /* 添加 translate */
}

.modal-header {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ddd;
  background: linear-gradient(to right, #333173, #316cdf);
}

.modal-title {
  font-size: 16px;
  font-weight: bold;
}

.modal-close {
  background: transparent;
  border: none;
  font-size: 16px;
  cursor: pointer;
}

.modal-content {
  /* padding: 20px; */
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes zoomIn {
  from {
    transform: translate(-50%, -50%) scale(0.7); /* 添加 translate */
  }
  to {
    transform: translate(-50%, -50%) scale(1); /* 添加 translate */
  }
}

@keyframes zoomOut {
  from {
    transform: translate(-50%, -50%) scale(1); /* 添加 translate */
  }
  to {
    transform: translate(-50%, -50%) scale(0.7); /* 添加 translate */
  }
}
.modal-title {
  font-size: 16px;
  font-weight: bold;
}
.modal-xian {
  width: 83px;
  height: 4px;
  background: linear-gradient(270deg, #ebedf8 0%, rgba(255, 255, 255, 0) 100%);
}
.yuan {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background: linear-gradient(90deg, #ebedf8 0%, rgba(255, 255, 255, 0) 100%);
}
</style>
