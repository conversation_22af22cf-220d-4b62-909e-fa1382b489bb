<template>
  <div class="text-container">
    <div ref="textWrapper" :class="['text-wrapper', { expanded: isExpanded }]">
      <span ref="text" class="text">{{ content }}</span>
    </div>
    <button v-if="showExpandButton" @click="toggleExpand" class="btn">
      {{ isExpanded ? "收起" : "展开全文" }}
    </button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isExpanded: false,
      showExpandButton: false,
      content:
        "这是一个示例文本，用于展示如何在两行文本溢出隐藏的情况下，显示一个展开全文的按钮。当点击按钮时，可以展开显示所有文本。",
    };
  },
  mounted() {
    this.checkOverflow();
    window.addEventListener("resize", this.checkOverflow);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.checkOverflow);
  },
  methods: {
    checkOverflow() {
      this.$nextTick(() => {
        const textWrapper = this.$refs.textWrapper;
        const textElement = this.$refs.text;

        // 检查文本是否溢出
        const isOverflowing =
          textElement.scrollHeight > textWrapper.clientHeight;
        this.showExpandButton = isOverflowing;
      });
    },
    toggleExpand() {
      this.isExpanded = !this.isExpanded;

      // 重新检查溢出状态
      if (!this.isExpanded) {
        this.$nextTick(this.checkOverflow);
      }
    },
  },
};
</script>

<style scoped>
.text-container {
  max-width: 300px; /* 根据需要调整宽度 */
  background-color: #f0f0f0; /* 示例背景色，可根据需要调整 */
  border-radius: 4px;
  position: relative;
}

.text-wrapper {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  position: relative;
  transition: max-height 0.3s ease;
  max-height: 3.6em; /* 根据字体大小调整 */
}

.text-wrapper.expanded {
  -webkit-line-clamp: unset;
  max-height: none;
}

.text {
  height: 48px;
  display: inline;
  white-space: normal; /* 确保在多行情况下工作 */
}

.btn {
  background-color: #007bff; /* 按钮背景色 */
  color: #fff;
  border: none;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 12px;
  border-radius: 4px;
  margin-top: 10px;
  display: block; /* 确保按钮显示在新行 */
}
</style>
