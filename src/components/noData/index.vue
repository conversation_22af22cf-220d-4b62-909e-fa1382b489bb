<!-- 暂无数据 -->
<template>
  <div class="noData">
    <svg-icon icon-class="NoData" />
    <span>{{ text ? text : "暂无数据" }} </span>
  </div>
</template>

<script>
export default {
  props: ["text"],
  data() {
    return {};
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {},
  //方法所在
  methods: {},
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {}
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.noData {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50px;

  .svg-icon {
    width: 174px;
    height: 130px;
  }

  span {
    margin-top: 20px;
    color: #333;
    font-size: 16px;
    line-height: 28px;
  }
}
</style>
