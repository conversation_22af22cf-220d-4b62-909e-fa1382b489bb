<template>
  <div class="frame">
    <div class="timeline flex" :style="{ width: 10 * 200 + 'px' }">
      <div
        class="item"
        v-for="(i, index) in 10"
        :style="{
          borderBottom: index === 9 ? 'none' : '1px solid #5585ec',
          width: index === 9 ? '120px' : '200px',
          marginLeft: index === 0 ? '100px' : '0',
        }"
      >
        <div class="node up"></div>
        <div class="node below"></div>
        <div class="time">2023-12-13 11:12:21</div>
        <div class="title ellipsis">提示单下发</div>
        <div class="unit ellipsis">市交通局</div>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {},
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style scoped lang="less">
.frame {
  width: 100%;
  overflow-x: auto;
  padding: 10px;
}

.timeline {
  height: 80px;

  .item {
    height: 50px;
    width: 200px;
    border-left: 1px solid #5585ec;
    position: relative;

    .title {
      color: #333333;
      font-size: 14px;
      line-height: 20px;
      padding-left: 10px;
    }

    .unit {
      color: #666666;
      font-size: 14px;
      line-height: 20px;
      padding-left: 10px;
    }

    .node {
      width: 11px;
      height: 11px;
      background: #ffffff;
      border: 3px solid #5585ec;
      border-radius: 50%;
      position: absolute;
    }

    .up {
      top: 0;
      left: -5.5px;
    }

    .below {
      width: 12px;
      height: 12px;
      bottom: -6px;
      left: -6px;
    }

    .time {
      color: #666666;
      font-size: 14px;
      line-height: 20px;
      position: absolute;
      bottom: -30px;
      left: -60px;
    }
  }
}
</style>
