<template>
    <div class="videoContent" :style="{ width: videoWidth, height: videoHeight }" style="position: relative;">
        <Spin v-if="loading" fix>
            <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
        </Spin>
        <div id="play_window" :style="{ width: videoWidth, height: videoHeight }"></div>
        <div v-if="!videoLoad" @click="retryPlay">
            <span class="video-tips">视频加载失败请重试或切换流畅模式 </span>
        </div>

        <div style="z-index: 10;  position: absolute; right: 0px; bottom: 0px;">
            <Button-group>
                <Button type="ghost" @click='clickPlayType' :class="{ 'chooseButton': playType == 0 }">高清 </Button>
                <Button type="ghost" @click='clickPlayType1' :class="{ 'chooseButton': playType == 1 }">流畅1</Button>
                <Button type="ghost" @click='clickPlayType2' :class="{ 'chooseButton': playType == 2 }">流畅2</Button>
            </Button-group>
        </div>
    </div>
</template>
<script>
export default {
    name: 'hkvideo',
    props: {
        cameraIndexCode: {
            type: String,
            required: true
        },
        videoWidth: {
            type: String,
            required: true
        },
        videoHeight: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            myPlugin: null,
            loading: false,
            videoLoad: true,
            timeId: "",
            playType: 0,
            wsUrl: ""
        }
    },
    mounted() {
        window.addEventListener('resize', () => {
            this.$nextTick(() => {
                if (this.myPlugin !== null) {
                    this.myPlugin.JS_Resize();
                }
            })
        })


        this.clickPlayType();
    },
    beforeDestroy() {
        this.myPlugin.JS_Stop(0);
        clearTimeout(this.timerId);
    },
    methods: {
        init() {
            this.initH5Player(1);
            this.play(this.wsUrl, 0);
        },
        initH5Player(split) {
            this.myPlugin = new window.JSPlugin({
                szId: 'play_window', //需要英文字母开头，唯一性，必填
                szBasePath: '../../../static/haikang/player/', // 必填,与h5player.min.js的引用目录一致 填写的是pulblic下的路径！！！
                bSupporDoubleClickFull: true,//是否支持双击全屏，默认true
                openDebug: false,
                oStyle: {
                    borderSelect: '#000',
                },
                // 当容器div#play_window有固定宽高时，可不传iWidth和iHeight，窗口大小将自适应容器宽高
                // iWidth: 200,
                // iHeight: 200,
                // 分屏播放，默认最大分屏4*4
                iMaxSplit: split,
                iCurrentSplit: split,
            })

            // 事件回调绑定
            this.myPlugin.JS_SetWindowControlCallback({
                windowEventSelect: function (iWndIndex) {  //插件选中窗口回调
                    console.log('windowSelect callback: ', iWndIndex);
                },
                pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {  //插件错误回调
                    console.log('pluginError callback: ', iWndIndex, iErrorCode, oError);
                },
                windowEventOver: function (iWndIndex) {  //鼠标移过回调
                    //console.log(iWndIndex);
                },
                windowEventOut: function (iWndIndex) {  //鼠标移出回调
                    //console.log(iWndIndex);
                },
                windowEventUp: function (iWndIndex) {  //鼠标mouseup事件回调
                    //console.log(iWndIndex);
                },
                windowFullCcreenChange: function (bFull) {  //全屏切换回调
                    console.log('fullScreen callback: ', bFull);
                },
                firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {  //首帧显示回调
                    console.log('firstFrame loaded callback: ', iWndIndex, iWidth, iHeight);
                },
                performanceLack: function () {  //性能不足回调
                    console.log('performanceLack callback: ');
                }
            });
        },
        play(url, index) {
            console.log("realUrl:" + url);
            this.loading = true;

            let that = this;

            this.timeId = setTimeout(function () {

                // 10秒未播放停止播放
                if (that.loading == true) {
                    that.loading = false;
                    that.videoLoad = false;
                    that.myPlugin.JS_Stop(index);
                }
            }, 20000);

            this.myPlugin.JS_Play(url,
                {
                    playURL: url, // 流媒体播放时必传
                    mode: 0, // 解码类型：0=普通模式; 1=高级模式 默认为0
                    // ...
                },
                index, //当前窗口下标
            ).then(
                () => {
                    this.loading = false;
                    this.videoLoad = true;
                    console.info('JS_Play success');
                    // do you want...
                },
                (err) => {
                    this.loading = false;
                    this.videoLoad = false;
                    this.myPlugin.JS_Stop(index);
                    console.log('播放失败' + JSON.stringify(err))
                    // do you want...
                }
            ).catch((e) => {
                this.loading = false;
                this.videoLoad = false;
                this.myPlugin.JS_Stop(index);
                this.$message.error('未获取到相关信息。' + e.message);
            });
        },
        retryPlay() {
            this.videoLoad = true;
            this.init();
        },
        async clickPlayType() {

            this.playType = 0;
            this.videoLoad = true;

            await this.getWsUrl(this.playType);
            this.init();
        },
        async clickPlayType1() {
            this.playType = 1;
            this.videoLoad = true;

            await this.getWsUrl(this.playType);
            this.init();
        },
        async clickPlayType2() {
            this.playType = 2;
            this.videoLoad = true;

            await this.getWsUrl(this.playType);
            this.init();
        },
        getWsUrl(streamType) {

            return new Promise((resolve, reject) => {
                // 异步操作 1
                let params = {
                    cameraIndexCode: this.cameraIndexCode,
                    streamType: streamType,
                    protocol: 'ws',
                    transmode: 'TCP',
                    expand: 'transcode=0',
                    streamform: 'pc'
                };
                this.$http
                    .get("/monitoringPoint/getMonitorRtspUrl", { params })
                    .then((res) => {
                        let realWsUrl = "";
                        if (res.body.status == 0) {
                            if (res.body.data) {
                                //this.rtspUrl = 'https://10.61.23.53:31443/flvApi/live?url=' + res.body.data; //生产
                                //this.rtspUrl = 'ws://10.61.23.53:37860/live?url=' + res.body.data;
                                // let haikang = "";
                                // if (res.body.data.substring(0, 22) == 'ws://100.192.0.153:559') {
                                //   haikang = 'haikang1';
                                // } else {
                                //   haikang = 'haikang2';
                                // }
                                console.log("res.body.data.substring(0, 21):" + res.body.data.substring(0, 22));
                                this.wsUrl = 'wss://10.61.23.53:31443' + res.body.data.substring(22);
                                // this.wsUrl = res.body.data;
                                console.log("this.wsUrl:" + this.wsUrl);
                                console.log("res.body.data:" + res.body.data);
                                // this.wsUrl = res.body.data;
                                // console.log("this.wsUrl2:" + this.wsUrl);
                                //this.trueRtspUrl = res.body.data; //生产
                                //this.rtspUrl = 'http://localhost:8866/live?url=' + res.body.data; //测试
                            } else {
                                this.$Message.error("摄像头不在线");
                            }
                        } else if (res.body.status == 2) {
                            this.$Message.error("一分钟以内仅能查看6次摄像头，请稍后重试");
                        } else if (res.body.status == 3) {
                            this.$Message.error("媒体组件所在服务器使用量达到上限");
                        }
                        resolve();
                    }).catch(err => {
                        reject(err);
                    });
            });
        },
    },
}
</script>

<style scoped>
/* 如果出警告不用管，原生css已支持这种层级写法无需安装css预编译器 */
.videoContent {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: black;
    border-radius: 4px;
    position: relative;

    .playWnd {
        width: 100%;
        height: 100%;
    }



    .ivu-spin-fix {
        background-color: rgba(0, 0, 0, 0.8);
    }
}

.video-tips {
    position: absolute;
    left: 50%;
    top: 50%;
    color: red;
    font-weight: bold;
    font-size: 24px;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 30;
}

.chooseButton {
    color: #5092e2;
}
</style>
