<template>
  <div class="file-preview">
    <VueOfficePPT
      v-if="fileUrl.indexOf('.ppt') !== -1"
      :src="baseUrl + fileUrl"
    />
    <VueOfficeExcel
      v-if="fileUrl.indexOf('.xls') !== -1"
      :src="baseUrl + fileUrl"
    />
    <VueOfficeDocx
      v-if="fileUrl.indexOf('.docx') !== -1"
      :src="baseUrl + fileUrl"
    />
    <VueOfficePDF
      v-if="fileUrl.indexOf('.pdf') !== -1"
      :src="baseUrl + fileUrl"
    />
  </div>
</template>

<script>
import VueOfficeDocx from "@vue-office/docx";
import VueOfficeExcel from "@vue-office/excel";
import VueOfficePPT from "@vue-office/pptx";
import VueOfficePDF from "@vue-office/pdf";

export default {
  components: {
    VueOfficeDocx,
    VueOfficeExcel,
    VueOfficePPT,
    VueOfficePDF,
    // PdfViewer,
  },
  props: {
    fileUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      fileType: "", // 文件类型
      pptThumbnails: [], // PPT缩略图
      currentPage: 1, // 当前显示的PPT页
      baseUrl: gl.minioUrl,
    };
  },
  computed: {
    isPDF() {
      return this.fileType === "pdf";
    },
    isPPT() {
      return this.fileType === "ppt" || this.fileType === "pptx";
    },
    isOfficeFile() {
      return ["doc", "docx", "xls", "xlsx", "ppt", "pptx"].includes(
        this.fileType
      );
    },
  },
  watch: {
    fileUrl: {
      immediate: true,
      handler(newVal) {
        this.detectFileType(newVal);
      },
    },
  },
  methods: {
    detectFileType(url) {
      const ext = url.split(".").pop().toLowerCase();
      this.fileType = ext;

      if (this.isPPT) {
        this.loadPPTThumbnails(url);
      }
    },
    async loadPPTThumbnails(url) {
      try {
        const ppt = new VueOfficePPT({ src: url });
        await ppt.load();
        this.pptThumbnails = ppt.getThumbnails();
      } catch (error) {
        console.error("PPT缩略图加载失败", error);
      }
    },
    onOfficeLoaded() {
      console.log("Office 文件加载完成");
    },
  },
};
</script>

<style scoped>
.file-preview {
  height: 100vh;
}
.ppt-thumbnails {
  width: 120px;
  overflow-y: auto;
  border-right: 1px solid #ddd;
  background: #f9f9f9;
}
.thumbnail-item {
  padding: 5px;
  cursor: pointer;
}
.thumbnail-item img {
  width: 100%;
  border-radius: 5px;
}
.thumbnail-item.active {
  border: 2px solid #007bff;
}
.file-viewer {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
