<template>
  <div class="keywordRule">
    <div class="title">监测账号填写规则：</div>
    <div>填写监测账号的昵称</div>
    <div>多个账号之间用空格分隔。例如：zhangsan lisi</div>
    <div>不同平台需要分别填写监测账号</div>
    <div>3个平台，不可同时为空</div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {},
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style scoped lang="less">
.keywordRule {
  width: 620px;
  height: 150px;
  background: #ebedf8;
  border-radius: 4px;
  padding: 10px 10px 20px 14px;
  color: #999999;
  font-size: 14px;
  line-height: 22px;
  .title {
    font-weight: 600;
  }
}
</style>
