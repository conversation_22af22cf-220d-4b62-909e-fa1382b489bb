<template>
  <div class="hover-image-preview">
    <!-- 只有当缩略图URL存在且有效时才显示 -->
    <div
      v-if="isValidUrl(thumbnailUrl)"
      class="thumbnail-container"
      @mouseenter="showPreview"
      @mouseleave="hidePreview"
    >
      <img
        :src="thumbnailUrl"
        class="thumbnail-image"
        :alt="alt || '缩略图'"
        @error="handleImageError"
      />
    </div>
    <span v-else>-</span>

    <!-- 预览容器 -->
    <div
      class="preview-container"
      v-show="isPreviewVisible && isValidUrl(imageUrl)"
      :style="previewStyle"
    >
      <img
        :src="imageUrl"
        class="preview-image"
        :alt="alt || '预览图'"
        @error="handlePreviewError"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "HoverImagePreview",
  props: {
    thumbnailUrl: {
      type: String,
      required: true
    },
    imageUrl: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isPreviewVisible: false,
      previewStyle: {
        top: '0px'
      }
    };
  },
  methods: {
    showPreview(event) {
      // 计算预览图片的位置，使其垂直居中
      const thumbnailRect = event.target.getBoundingClientRect();
      const viewportHeight = window.innerHeight;

      // 计算预览图片的顶部位置，确保其在视口内
      let topPosition = thumbnailRect.top - 100; // 默认向上偏移100px

      // 确保预览图片不会超出视口顶部
      if (topPosition < 0) {
        topPosition = 0;
      }

      // 确保预览图片不会超出视口底部
      if (topPosition + 400 > viewportHeight) { // 假设预览图片高度最大为400px
        topPosition = viewportHeight - 400;
      }

      this.previewStyle = {
        top: `${topPosition}px`
      };

      this.isPreviewVisible = true;
    },
    hidePreview() {
      this.isPreviewVisible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.hover-image-preview {
  position: relative;
  display: inline-block;

  .thumbnail-container {
    display: inline-block;

    .thumbnail-image {
      width: 80px;
      height: 60px;
      object-fit: cover;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .preview-container {
    position: fixed;
    left: calc(50% + 150px); // 在右侧显示
    z-index: 1000;
    padding: 10px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);

    .preview-image {
      max-width: 300px; // 设置最大宽度为300px
      max-height: 400px;
      object-fit: contain;
    }
  }
}
</style>
