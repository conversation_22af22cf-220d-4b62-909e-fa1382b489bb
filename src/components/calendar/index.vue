<template>
  <div class="calendar">
    <div class="header-week">
      <div class="item" v-for="(item, index) in weekList" :key="item.key">
        {{ item.value }}
      </div>
    </div>
    <div class="main-day">
      <div class="item" v-for="item in monthStartDay - 1" :key="item"></div>
      <div
        :class="[
          'item',
          item.periodsTime === selectDay ? 'active' : '',
          item.periodsTime > today ? 'disable' : ''
        ]"
        v-for="(item, index) in monthAllDay"
        :key="item.periodsTime"
        @click="cut(item)"
      >
        <span>
          {{ index + 1 }}
        </span>
        <div class="remark">{{ item.periods }}期</div>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import moment from "moment";

const weekList = [
  { key: 1, value: "一" },
  { key: 2, value: "二" },
  { key: 3, value: "三" },
  { key: 4, value: "四" },
  { key: 5, value: "五" },
  { key: 6, value: "六" },
  { key: 0, value: "日" }
];
export default {
  data() {
    // 这里存放数据
    return {
      moment,
      weekList,
      monthStartDay: 5,
      monthAllDay: [],
      selectDay: null,
      today: moment().valueOf(),

      periodsId: ""
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    years: {
      default: moment().year()
    },
    months: {
      default: moment().month()
    },
    defaultDay: {
      default: moment().valueOf()
    }
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.selectDay = this.defaultDay;
  },
  // 方法集合
  methods: {
    //获取期数
    getPeriods(month) {
      const firstDayOfMonth = moment([moment().year(), month])
        .startOf("month")
        .format("YYYY-MM-DD");
      const lastDayOfMonth = moment([moment().year(), month])
        .endOf("month")
        .format("YYYY-MM-DD");
      let params = {
        startDate: firstDayOfMonth,
        endDate: lastDayOfMonth
      };
      console.log(this.monthAllDay.length);
      this.$http.get("/material/periodsList", { params }).then(res => {
        if (res.body.status === 0) {
          this.monthAllDay = res.body.data;

          this.monthAllDay.forEach(i => {
            if (i.periodsTime === this.selectDay) {
              this.$emit("change", {
                ...i,
                periods: i.periods
              });
            }
          });
        }
      });
    },
    cut(d) {
      if (d.periodsTime <= this.today) {
        this.selectDay = d.periodsTime;
        this.periodsId = d.periods;
        this.$emit("change", {
          ...d,
          periods: this.periodsId
        });
      }
    },
    // 获取月份的第一天是周几，用于日历的开始位置定位
    getFirstDayOfMonth(year, month) {
      // 获取指定月份的第一天
      // const firstDayOfMonth = moment([year, month]).startOf("month");
      const firstDayOfMonth = moment([year, month]).startOf("month");
      // 获取第一天是星期几，0 表示星期日，1 表示星期一，以此类推
      this.monthStartDay =
        firstDayOfMonth.day() === 0 ? 7 : firstDayOfMonth.day();
    }
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    monthStartDay(d) {
      console.log(d);
    }
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    // 获取指定月份的第一天是周几
    this.getFirstDayOfMonth(this.years, this.months);

    this.getPeriods(this.months);

    //获取默认值-今天
    // this.selectDay = moment().format("YYYY-MM-DD");
  }
};
</script>

<style scoped lang="less">
.calendar {
  padding: 5px;

  .item {
    width: calc(~"100% / 7");
    text-align: center;
    font-size: 14px;
  }

  .header-week {
    display: flex;
    border-bottom: 1px solid #999999;
    line-height: 30px;
  }

  .main-day {
    display: flex;
    flex-wrap: wrap;

    .item {
      margin: 6px 0;
      aspect-ratio: 1 / 1; /* 设置宽高比为 1:1 */
      cursor: pointer;

      .remark {
        font-size: 12px;
      }
    }

    .active {
      background-color: #5585ec;
      border-radius: 50%;
      color: #fff;
    }

    .disable {
      color: #aaa;
      cursor: no-drop;
    }
  }
}
</style>
