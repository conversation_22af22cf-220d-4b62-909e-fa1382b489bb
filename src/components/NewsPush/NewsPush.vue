<template>
  <div class="newPush_frame">
    <div
      class="item_frame"
      :style="{ right: start ? '20px' : '-450px', bottom: getBottom() }"
    >
      <div class="header">
        <div>
          <svg-icon icon-class="预警弹窗" v-if="data.msgType != 'checkMsg'">
          </svg-icon>
          <span class="title">
            {{ data.msgType == "checkMsg" ? "消息通知" : "预警提醒" }}
          </span>
        </div>
        <div v-if="maximize || minimize" class="title">
          <span
            style="color: blue; cursor: pointer"
            @click="
              () => {
                maximize = true;
                minimize = false;
              }
            "
            >{{ dataList.length }}条</span
          >预警信息
        </div>
        <div>
          <Icon
            v-if="!minimize"
            type="ios-remove"
            title="最小化"
            class="close"
            @click="
              () => {
                maximize = false;
                minimize = true;
                dataList = [];
              }
            "
          />
          <Icon
            v-if="maximize || minimize"
            type="ios-browsers-outline"
            title="正常化"
            class="close"
            @click="
              () => {
                maximize = false;
                minimize = false;
                maximizeTips ? (maximizeTips = false) : maximizeTips;
              }
            "
          ></Icon>
          <span title="最大化">
            <svg-icon
              icon-class="mininize"
              v-if="!maximize"
              type="ios-expand"
              class="close"
              title="最大化"
              style="font-size: 12px"
              @click.native="
                () => {
                  maximize = true;
                  minimize = false;
                  minimizeTips = true;
                  maximizeTips = false;
                  dataTipsList = [];
                }
              "
            >
            </svg-icon>
          </span>

          <Icon type="md-close" class="close" @click="close" title="关闭" />
        </div>
      </div>
      <div
        class="content"
        :style="{
          height: maximize ? 'calc(100vh - 150px)' : minimize ? '0px' : '170px',
        }"
      >
        <template v-for="(item, index) in dataList">
          <div
            class="listFrame"
            :key="index"
            v-if="maximize ? true : index == 0"
          >
            <div class="content_title">
              <span v-if="!maximize">
                接收到{{ dataList.length }}条新的信息
              </span>
              <span v-else></span>
              <span>
                {{ item.mpublishTime | dateFormat("yyyy-MM-dd HH:mm:ss") }}
              </span>
            </div>
            <div class="listItem">
              <div class="text two-ellipsis">{{ item.mtitle }}</div>
              <div class="checkDetails">
                <a :href="getPath(item)" target="_blank" @click="toRead(item)"
                  >查看详情</a
                >
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <div
      class="item_frame"
      :style="{ right: startTips ? '20px' : '-450px', bottom: getBottomTips() }"
    >
      <div class="header">
        <div>
          <svg-icon icon-class="消息提示" v-if="data.msgType != 'checkMsg'">
          </svg-icon>
          <span class="title">
            {{ data.msgType == "checkMsg" ? "消息通知" : "提示单反馈提醒" }}
          </span>
        </div>
        <div v-if="maximizeTips || minimizeTips" class="title">
          <span
            style="color: blue; cursor: pointer"
            @click="
              () => {
                maximizeTips = true;
                minimizeTips = false;
              }
            "
            >{{ dataTipsList.length }}条</span
          >提示单反馈
        </div>
        <div>
          <Icon
            v-if="!minimizeTips"
            type="ios-remove"
            title="最小化"
            class="close"
            @click="
              () => {
                maximizeTips = false;
                minimizeTips = true;
                dataTipsList = [];
              }
            "
          />
          <Icon
            v-if="maximizeTips || minimizeTips"
            type="ios-browsers-outline"
            title="正常化"
            class="close"
            @click="
              () => {
                maximizeTips = false;
                minimizeTips = false;
                maximize ? (maximize = false) : maximize;
              }
            "
          ></Icon>
          <span title="最大化">
            <svg-icon
              icon-class="mininize"
              v-if="!maximizeTips"
              type="ios-expand"
              class="close"
              title="最大化"
              style="font-size: 12px"
              @click.native="
                () => {
                  maximizeTips = true;
                  minimizeTips = false;
                  minimize = true;
                  maximize = false;
                  dataTips = [];
                }
              "
            >
            </svg-icon>
          </span>

          <Icon type="md-close" class="close" @click="closeTips" title="关闭" />
        </div>
      </div>
      <div
        class="content"
        :style="{
          height: maximizeTips
            ? 'calc(100vh - 150px)'
            : minimizeTips
            ? '0px'
            : '170px',
        }"
      >
        <template v-for="(item, index) in dataTipsList">
          <div
            class="listFrame"
            :key="index"
            v-if="maximizeTips ? true : index == 0"
          >
            <div class="content_title">
              <span v-if="!maximizeTips">
                接收到{{ dataTipsList.length }}条新的提示单反馈
              </span>
              <span v-else></span>
              <span>
                {{ item.publishTime | dateFormat("yyyy-MM-dd HH:mm:ss") }}
              </span>
            </div>
            <div class="listItem">
              <div class="text two-ellipsis">
                【编号：{{ item.promptNum }}】{{ item.msgAbstract }}
              </div>
              <div class="checkDetails">
                <a
                  :href="'/main/tipsDeatil?promptNum=' + item.promptNum"
                  target="_blank"
                  @click="toRead(item)"
                  >查看详情</a
                >
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div
      class="item_frame"
      :style="{
        right: startEvent ? '20px' : '-450px',
        bottom: getBottomTips(),
      }"
    >
      <div class="header">
        <div>
          <svg-icon icon-class="暂停提醒"> </svg-icon>
          <span class="title"> 事件分析到期暂停提醒 </span>
        </div>
      </div>
      <div class="content" style="height: 170px">
        <div class="articleTile ellipsis">{{ eventData.eventName }}</div>
        <div class="warnText">
          事件{{ RemainingDays }}后将<span class="highlight">暂停更新</span
          >，如需继续监测，请点击“延长监测”。
        </div>
        <div class="controls flex sb">
          <div class="btn cp" @click="prolonging">延长监测一个月</div>
          <div class="Off cp" @click="startEvent = false">我知道了</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import situations from "@/assets/json/situations.json";
import moment from "moment";

// const
export default {
  name: "index",
  components: {},
  data() {
    return {
      start: false,
      startTips: false, //提示单
      startEvent: false, //事件提醒
      RemainingDays: 0, //剩余天数
      eventData: {},
      data: {},
      url: "/DetailsPage/WeiboDetail",
      situations,
      type: "msg",
      dataList: [],
      dataTipsList: [],
      maximize: false,
      minimize: false,
      maximizeTips: false,
      minimizeTips: false,
      lockReconnect: false, //是否真正建立连接
      timeout: 50 * 1000, //58秒一次心跳
      timeoutObj: null, //心跳心跳倒计时
      serverTimeoutObj: null, //心跳倒计时
      timeoutnum: null, //断开 重连倒计时
    };
  },
  methods: {
    prolonging() {
      let params = {
        eventId: this.eventData.eventId,
      };
      this.$http.get("/monitor/event/delayEvent", { params }).then((res) => {
        console.log(res);
        if (res.body.status === 0) {
          this.startEvent = false;
          this.$Message.success("事件监测时间延长成功！");
        } else {
          this.$Message.error("事件监测时间延长失败！");
        }
      });
    },
    getBottom() {
      if (this.startTips) {
        if (this.minimizeTips && !this.minimize) {
          return "90px";
        }
      }
    },
    getBottomTips() {
      if (this.start) {
        if (this.minimize) {
          return "90px";
        } else if (!this.minimize && !this.maximize && !this.minimizeTips) {
          return "260px";
        } else if (this.minimizeTips) {
          return "20px";
        }
      }
    },
    close() {
      this.start = false;
      this.dataList = [];
    },
    closeTips() {
      this.startTips = false;
      this.dataTipsList = [];
    },
    sendHeard(myws) {
      if (!myws) {
        return;
      }
      let param = {
        userName: localStorage.getItem("userName"),
        userAccount: localStorage.getItem("userAccount"),
        departmentName: localStorage.getItem("departmentName"),
        ip: localStorage.getItem("ip"),
        browser: localStorage.getItem("browser"),
        env: process.env.RUN_MODE == "build" ? "prod" : "test",
      };
      myws.send(JSON.stringify(param)); //这里可以自己跟后端约定
    },
    //webSocket
    getData() {
      let ws = new WebSocket(
        process.env.NEW_PUSH_SOCKET_URL + localStorage.getItem("userAccount")
      );

      ws.onopen = () => {
        //开启心跳
        this.startWs(ws);
        this.sendHeard(ws);
      };
      //链接关闭事件
      ws.onclose = (event) => {
        this.reconnect();
      };
      //获取后端的数据
      ws.onmessage = (data) => {
        if (data.data.indexOf("{") == 0) {
          let a = JSON.parse(data.data);
          console.log(a);
          if(a.noticeType == 2 || a.noticeType == 3){
            return false;
          }
          if (a.msgType === "monitorEvent") {
            this.startEvent = true;
            this.eventData = a;
            console.log(moment(*************).format("YYYY-MM-DD HH:mm:ss"));
            this.RemainingDays = this.getRemainingTime(a.monitorStopTime);
            return false;
          }

          if (data.data && a.type) {
            if (this.dataList.length == 0 && !this.start) {
              this.maximize = false;
              this.minimize = false;
            }
            this.start = true;
            this.dataList.push(a);
          }
        }
        if (data.data.indexOf("[") == 0) {
          let a = JSON.parse(data.data);
          if (data.data) {
            if (this.dataTipsList.length == 0 && !this.startTips) {
              this.maximizeTips = false;
              this.minimizeTips = false;
            }
            this.startTips = true;
            this.dataTipsList = a;
          }
        }

        //收到服务器信息，心跳重置
        this.reset(ws);
      };
      ws.onerror = (data) => {
        //收到服务器信息，心跳重置
        // console.log("链接错误:" + data);
        this.reconnect();
      };
    },
    getRemainingTime(targetDateStr) {
      // 解析目标日期
      const targetDate = moment(targetDateStr);
      // 获取当前日期
      const now = moment();

      // 计算目标日期与当前日期相差的总分钟数
      const diffInMinutes = targetDate.diff(now, "minutes");
      // 转换为天和小时
      const daysUntilTarget = Math.floor(diffInMinutes / (60 * 24)); // 转换为天数
      const remainingHours = Math.floor((diffInMinutes % (60 * 24)) / 60); // 计算剩余小时数

      // 超过1天的情况，返回天数
      if (daysUntilTarget >= 1) {
        return `${daysUntilTarget}天`;
      }
      // 不足1天的情况，返回小时数或不足1小时的提示
      else if (remainingHours >= 1) {
        return `${remainingHours}小时`;
      }
      // 小于1小时的情况
      else {
        return "剩余不足1小时";
      }
    },
    getPath(data) {
      let param = "?msgKey=" + data.mkey + "&situation=" + data.situation;
      url = "/main/details" + param;
      return url;
      let url;
      if (data.type == 1 || data.situation == "10" || data.situation > 10) {
        let param =
          "?msgKey=" +
          data.mkey +
          "&situation=" +
          data.situation +
          "&msgType=2&moduleId=" +
          data.alertId +
          "&objId=1&isWaring=true&alertId=" +
          data.alertId +
          "&sensScore=" +
          (data.sensScore ? data.sensScore : "") +
          "&keywordName=" +
          (data.keywordName ? data.keywordName : "");
        if (
          data.situation == 10 ||
          data.situation == 140 ||
          data.situation == 150
        ) {
          url = "/DetailsPage/WeiboDetail" + param + "&ugchKey=" + data.ugchKey;
        } else {
          url = "/DetailsPage/NewsDetail" + param;
        }
      }
      if (
        data.type == 2 ||
        data.situation == "2" ||
        data.situation == "3" ||
        data.situation == "4" ||
        data.situation == "5"
      ) {
        let param =
          "?msgKey=" +
          data.msgKey +
          "&situation=" +
          data.situation +
          "&publishTime=" +
          data.msgPublishTime +
          "&ugchKey=" +
          data.ugchKey +
          "&msgType=1&objId=1&noHitWord=1&moduleId=" +
          data.alertId +
          "&uname=" +
          data.uname +
          "&alertId=" +
          data.alertId +
          "&sensScore=" +
          (data.sensScore ? data.sensScore : "") +
          "&keywordName=" +
          (data.keywordName ? data.keywordName : "");
        if (data.situation == 2) {
          url = "/DetailsPage/AutoDetail" + param;
        } else {
          url = "/DetailsPage/VideoDetail" + param;
        }
      }
      return url;
    },
    toRead(data) {
      return;
      if (data.alertId && !data.readAccount) {
        // 标记已读
        this.$http
          .get(
            "/alert/msg/read?alertId=" +
              data.alertId +
              "&type=" +
              (data.type == 2 || data.situation <= 5 ? "media" : "msg")
          )
          .then((res) => {
            if (res.data.status == 0) {
              data.readTime = new Date();
            } else {
            }
          });
      }
    },

    reconnect() {
      //重新连接
      var that = this;
      // console.log("重新链接lockReconnect:" + that.lockReconnect);
      if (that.lockReconnect) {
        return;
      }
      that.lockReconnect = true;
      //没连接上会一直重连，设置延迟避免请求过多
      that.timeoutnum && clearTimeout(that.timeoutnum);
      that.timeoutnum = setTimeout(function () {
        //新连接
        that.getData();
        that.lockReconnect = false;
      }, 3000);
    },
    reset(myws) {
      //重置心跳
      var that = this;
      //清除时间
      clearTimeout(that.timeoutObj);
      clearTimeout(that.serverTimeoutObj);
      //重启心跳
      that.startWs(myws);
    },
    startWs(myws) {
      //开启心跳
      var self = this;
      self.timeoutObj && clearTimeout(self.timeoutObj);
      self.serverTimeoutObj && clearTimeout(self.serverTimeoutObj);
      self.timeoutObj = setTimeout(function () {
        //这里发送一个心跳，后端收到后，返回一个心跳消息， 长时间没有消息通信，后端会自动断开
        if (myws.readyState == 1) {
          //如果连接正常
          self.sendHeard(myws);
        } else {
          //否则重连
          self.reconnect();
        }
        self.serverTimeoutObj = setTimeout(function () {
          //超时关闭
          myws.close();
        }, self.timeout);
      }, self.timeout);
    },
  },
  filters: {
    dateFormat(num, fmt) {
      // console.log(num, fmt);
      var date;
      if (num && num.length >= 16) {
        date = new Date(num);
      } else {
        var tmp = parseInt(num);
        const len = (tmp + "").length;
        let tmpDate = "";
        len === 10 ? (tmpDate = tmp * 1000) : (tmpDate = tmp);
        date = new Date(tmpDate);
      }
      const opt = {
        "y+": date.getFullYear().toString(), // 年
        "M+": (date.getMonth() + 1).toString(), // 月
        "d+": date.getDate().toString(), // 日
        "H+": date.getHours().toString(), // 时
        "m+": date.getMinutes().toString(), // 分
        "s+": date.getSeconds().toString(), // 秒
      };
      let ret;
      for (const k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length === 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
          );
        }
      }
      //xz用户提的问题，修改格式
      if (fmt) {
        let a = fmt.substr(11, 19);
        if (a == "00:00:00") {
          return fmt.substr(0, 11);
        }
      }
      return fmt;
    },
  },
  mounted() {
    // if (!gl.develop) {
    //本地为了调试方便，不弹窗
    this.getData();
    // }
  },
};
</script>

<style lang="less" scoped>
.item_frame {
  z-index: 9999;
  width: 450px;
  background: #ffffff;
  box-shadow: 0px 4px 12px 0px rgba(9, 11, 30, 0.25);
  border-radius: 4px;
  position: fixed;
  bottom: 20px;
  right: -450px;
  transition: 0.3s;

  .header {
    width: 100%;
    height: 50px;
    border-bottom: 1px solid #dfe3ed;
    padding: 0 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    /deep/ .ivu-icon {
      font-size: 24px;
    }

    .title {
      font-family: PingFangSC-Semibold;
      font-size: 14px;
      color: #383f4f;
      font-weight: 600;
    }

    .close {
      cursor: pointer;
      margin: 0 0 0 10px;
      font-size: 18px;
    }
  }

  .content {
    overflow-y: auto;
    transition: height 0.3s;
    padding: 0 20px;

    .content_title {
      display: flex;
      justify-content: space-between;
      height: 20px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #626772;
      line-height: 20px;
      font-weight: 400;
    }

    .listFrame {
      padding: 20px 0;
      border-bottom: 1px solid #eee;

      .listItem {
        margin-top: 20px;

        .text {
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          font-family: PingFangSC-Semibold;
          font-size: 16px;
          color: #383f4f;
          text-align: justify;
          font-weight: 600;
          min-height: 48px;
        }

        .checkDetails {
          margin-top: 20px;
          font-family: PingFangSC-Semibold;
          font-size: 14px;
          color: #1c7ff1;
          text-align: justify;
          line-height: 20px;
          font-weight: 600;
          text-decoration: underline;
          cursor: pointer;
        }
      }
    }
    .articleTile {
      color: #5092e2;
      font-size: 18px;
      font-weight: bold;
      line-height: 50px;
    }
    .warnText {
      color: #555;
      font-size: 12px;
      .highlight {
        color: red;
        font-weight: bold;
      }
    }
    .controls {
      margin-top: 30px;
      .btn {
        padding: 0 10px;
        font-size: 14px;
        color: #fff;
        font-weight: bold;
        line-height: 30px;
        background-color: #005cc8;
      }
      .Off {
        text-align: end;
        height: 100%;
        color: #5097d2;
        text-decoration: underline;
      }
    }
  }
}
</style>
