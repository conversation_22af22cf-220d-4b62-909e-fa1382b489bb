<template>
  <div class="monitoringVideoList">
    <div class="videoBox">
      <div class="CornerMark">
        <svg-icon :icon-class="situations[data.situation]" />
        <span>
          {{ situations[data.situation] }}
        </span>
      </div>
      <Checkbox class="check" :label="data.mkey"></Checkbox>
      <VideoPlayer :options="videoOptions" @toDetails="toDetails" />
    </div>
    <div class="remarkControls">
      <div class="remark">
        <div class="title ellipsis">
          {{  data.mtitle||data.mcontent }}
        </div>
        <div class="info">
          <div class="ellipsis" style="width: 50%">{{data.uname}}</div>
          <div>{{ data.mpublishTime }}</div>
        </div>
        <div class="info">
          <div>
            <svg-icon icon-class="爱心" />
            {{ data.likeCnt }}
          </div>
          <div>
            <svg-icon icon-class="涉济报送-信息" />
            {{ data.replyCnt }}
          </div>
          <div>
            <svg-icon icon-class="星星" />
            {{ data.friendCnt }}
          </div>
          <div>
            <svg-icon icon-class="涉济报送-转发" />
            {{ data.forwardCnt }}
          </div>
        </div>
      </div>
      <slot name="controls"></slot>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import ListControls from "@/components/listControls";

import situations from "@/assets/json/situations.json";

import VideoPlayer from "@/components/videoPlayer";

export default {
  data() {
    // 这里存放数据
    return {
      situations,
      videoOptions: {
        autoplay: false,
        controls: true,
        sources: [
          {
            src: gl.minioUrl + "/jnservermsg/jccz/video/b5028c5d57a8735abba4fabb6886c773.mp4",
            type: "video/mp4",
          },
        ],
      },
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { ListControls, VideoPlayer },
  props: {
    data: {
      default: {},
    },
    index: {
      default: 1,
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    toDetails() {
      const { href } = this.$router.resolve({
        path: "/main/details",
        query: {
          msgKey: this.data.mkey,
          situation: this.data.situation,
          sourcePath: this.$route.path,
          sourceName: this.$route.name,
        },
      });
      window.open(href, "_blank");
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style scoped lang="less">
.monitoringVideoList {
  position: relative;
  padding: 6px 13px;

  &:hover {
    .ListControls {
      display: flex !important;
    }

    .remark {
      display: none;
    }
  }

  .videoBox {
    width: 310px;
    height: 259px;
    position: relative;
    border-radius: 8px 8px 0 0;
    overflow: hidden;

    .video {
      width: 310px;
      height: 259px;
      position: absolute;
      background-color: #000;
      z-index: 0;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }

  .remarkControls {
    width: 310px;
    height: 85px;
    background: #ffffff;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    position: relative;

    .ListControls {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
    }

    /deep/ .ListControls {
      background-color: #fff;

      .item {
        margin-top: 10px;
        margin-right: 2px;
      }
    }

    .remark {
      padding: 12px 10px;

      .title {
        color: #333333;
        font-size: 14px;
      }

      .info {
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
        color: #666666;
        font-size: 12px;
      }
    }
  }

  .CornerMark {
    z-index: 2;
    position: absolute;
    left: 0;
    top: 0;
    //width: 70px;
    height: 26px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    line-height: 26px;

    font-size: 14px;
    padding-left: 10px;
  }

  .check {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1;
  }
}
</style>
