<template>
  <div class="contents">
    <!--分隔线-->
    <div class="xian-x"></div>
    <div class="xian-y"></div>

    <!-- 顶部搜索 -->
    <div class="header">
      <span class="sort">分类</span>
      <div class="sorts">
        <span class="sort">今日已添加素材</span>
        <div style="display: flex">
          <!-- 搜索框 -->
          <div class="inputs">
            <Input
              v-model="value"
              placeholder="请输入关键词"
              style="width: 240px"
            />
            <div class="btn">
              <svg-icon
                icon-class="素材库搜索"
                style="width: 23.17px; height: 23.17px"
              />
            </div>
          </div>
          <!-- 新建文件夹 -->
          <div class="addFile" v-show="dataId == 1" @click="openNewFolder">
            <svg-icon
              icon-class="弹框-新建文件夹"
              style="width: 18px; height: 14.87px; margin-right: 3.81px"
            />新建文件夹
          </div>
        </div>
      </div>
    </div>

    <!-- 个人素材及网情要报素材、右侧列表 -->
    <div style="display: flex; justify-content: space-between">
      <div class="list-nav">
        <span
          class="navs"
          v-for="item in titleList"
          :key="item.id"
          :class="item.id == dataId ? 'navs-select' : ''"
          @click="handleChange(item.id)"
          >{{ item.title }}</span
        >
      </div>
      <div class="lists">
        <!--网情要报素材列表  -->
        <div v-show="dataId == 2">
          <div
            class="lists-wq"
            v-for="(item, index) in dataList"
            :key="item.id"
          >
            <p style="display: flex">
              <span>{{ index + 1 }}.</span>
              <span
                style="width: 380px; margin-right: 10px"
                class="ellipsis"
                :title="item.msgTitle"
                >{{ item.msgTitle }}</span
              >
              <span>{{ moment(item.createTime).format("YYYY-MM-DD") }}</span>
            </p>
            <p
              class="ellipsis-2"
              style="margin-top: 10px"
              :title="item.msgAbstract ? item.msgAbstract : item.mabstract"
            >
              {{ item.msgAbstract || item.mabstract }}
            </p>
          </div>
        </div>

        <!-- 个人素材 -->
        <div v-show="dataId == 1">
          <div style="margin-left: 15px; margin-top: 5px">
            <div
              style="margin-top: 10px"
              v-for="item in classifyList"
              :key="item.classifyId"
            >
              <div
                style="cursor: pointer"
                @click="selectClassify(item)"
                :class="selectedClassifyId === item.classifyId ? 'active' : ''"
              >
                {{ item.classifyName }}
              </div>
              <div
                class="folder"
                :class="selectedFolderId === folder.classifyId ? 'active' : ''"
                v-for="folder in item.childList"
                :key="folder.classifyId"
                @click="selectFolder(item, folder)"
              >
                <div style="line-height: 20px">
                  <svg-icon
                    icon-class="文件夹"
                    style="width: 25px; height: 20px"
                  ></svg-icon>
                  <span style="margin-left: 10px">{{
                    folder.classifyName
                  }}</span>
                </div>
                <svg-icon
                  v-if="selectedFolderId === folder.classifyId"
                  icon-class="文件夹选中"
                  style="width: 15px; height: 15px; margin-right: 10px"
                ></svg-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

     <div class="foot">
      <span class="foots" @click="submit()">确定</span>
      <span
        class="foots"
        style="margin-left: 70px; background: #999999"
        @click="close()"
        >关闭</span
      >
    </div>



    <Modal v-model="newFolderModel" :title="folderModelName">
      <div class="flex">
        <span style="line-height: 32px">文件夹： </span>
        <Input v-model="value" style="width: 410px" placeholder="" />
      </div>
      <div slot="footer" class="box">
        <div class="modalBtn" @click="newFolder">确定</div>
        <div class="modalBtn" @click="newFolderModel = false">关闭</div>
      </div>
    </Modal>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import moment from "moment";

export default {
  name: "index.vue",
  data() {
    // 这里存放数据
    return {
      value: "", //输入框数据
      titleList: [
        { id: 2, title: "网情要报素材" },
        { id: 1, title: "个人素材" },
      ], //素材头部
      dataId: 2,
      dataList: [], //列表数据
      classifyList: [],
      selectedFolderId: null,
      selectedClassifyId: null,
      newFolderModel: false,
      folderModelName: ""
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},

  props: {
    data:{
      default:() => []
    },
    that:{}
  },
  // 方法集合
  methods: {
    moment,
     submit(){
      if (this.dataId == 1 && !this.selectedFolderId) {
          this.$Message.error("请选择文件夹")
        } else {
           this.$emit('handleMaterial',this.data,this.dataId, this.selectedFolderId)
           this.close()
        }
    },
    close(){
      this.$emit('close')
    },
    // 左侧头部切换
    handleChange(id) {
      this.dataId = id;
      if (this.dataId == 1) {
        this.getClassifyList();
      } else if (this.dataId == 2) {
        this.getList();
      }
    },

    openNewFolder() {
      if (this.selectedClassifyId) {
        this.folderModelName = "新建文件夹";
        this.newFolderModel = true;
      } else {
        this.$Message.error("请选择分类");
      }
    },

    newFolder() {
      let params = {
        parentId: this.selectedClassifyId,
        classifyName: this.value,
        operation: 1,
        level: 2,
      };
      this.that.$http
        .post("/classify/operationClassify", params, { emulateJSON: true })
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.success("添加成功！");
            this.newFolderModel = false;
            this.value = "";
            this.getClassifyList();
          }
        });
    },
    getList() {
      let params = {
        pageNo: 1,
        pageSize: 10,
        timeList: [
          {
            startDate: moment(new Date()).format("YYYY-MM-DD " + "00:00:00"),
            endDate: moment(new Date()).format("YYYY-MM-DD " + "23:59:59"),
          },
        ],
      };
      this.that.$http.get("/material/materialList", { params }).then((res) => {
        let data = res.body.data;
        data.list.forEach((i) => {
          if (i.msgEmotion !== null) {
            i.msgEmotion = i.msgEmotion.toString();
          }
          if (i.msgLevel !== null) {
            i.msgLevel = i.msgLevel.toString();
          }
        });
        this.dataList = data.list;
        // this.total = data.count;
      });
    },

    getClassifyList() {
      this.that.$http.get("/classify/classifyList").then((res) => {
        if (res.body.status === 0) {
          this.classifyList = res.body.data;
        } else {
          this.$Message.error("服务器错误！");
        }
      });
    },

    selectFolder(classify, folder) {
      this.selectedClassifyId = classify.classifyId;
      this.selectedFolderId = folder.classifyId;
    },

    selectClassify(classify) {
      this.selectedClassifyId = classify.classifyId;
      this.selectedFolderId = null
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getList();
  },
};
</script>

<style scoped lang="less">
.contents {
  position: relative;
  .xian-x {
    position: absolute;
    top: 69px;
    left: 0px;
    width: 100%;
    border-top: 1px dashed #707070;
  }
  .xian-y {
    position: absolute;
    top: 0px;
    left: 139.5px;
    height: calc(~"100% - 50px");
    // height: 600px;
    border-right: 1px dashed #707070;
  }
  .header {
    display: flex;
    font-family: PingFang SC;
    font-weight: 600;
    color: #333333;
    font-size: 16px;
    .sort {
      display: inline-block;
      width: 140px;
      height: 69px;
      line-height: 69px;
      text-align: center;
    }
    .sorts {
      width: 100%;
      padding-right: 13px;
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .inputs {
      position: relative;
      /deep/ .ivu-input {
        height: 40px;
        line-height: 40px;
        border: 1px solid #3b5576;
        font-family: PingFang SC;
        color: #999999;
        font-size: 14px;
      }
      .btn {
        position: absolute;
        right: -1px;
        top: 0px;
        width: 60px;
        height: 40px;
        background: #5585ec;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
      }
    }
    .addFile {
      width: 112px;
      height: 40px;
      font-family: PingFang SC;
      color: #ffffff;
      font-size: 16px;
      display: flex;
      cursor: pointer;
      justify-content: center;
      align-items: center;
      background: #5585ec;
      border-radius: 4px;
      margin-left: 14px;
      font-weight: 500;
    }
  }
  .list-nav {
    display: flex;
    flex-direction: column;
    padding: 12px 4.5px 0px 5px;
    .navs {
      display: inline-block;
      width: 130px;
      height: 40px;
      line-height: 40px;
      border-radius: 8px;
      font-family: PingFang SC;
      color: #333;
      font-size: 16px;
      text-align: center;
      margin-bottom: 12px;
      cursor: pointer;
    }
    .navs-select {
      background: #5585ec;
      color: #ffffff;
    }
  }
  .lists {
    padding: 10px 11px 0px 10px;
    font-family: PingFang SC;
    color: #333333;
    font-size: 16px;
    max-height: 400px;
    overflow-y: auto;
    flex: 1;
    .lists-wq {
      height: 100px;
      width: 530px;
      background: #ebedf8;
      padding: 10px 8px 14px 8px;
      margin-bottom: 12px;
    }
    .lists-gr {
      padding-left: 10px;
      height: 50px;
      width: 530px;
      background: #fff;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
  }

  .folder {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 10px;
    margin-top: 10px;
    padding: 10px;
    cursor: pointer;
  }

  .active {
    background-color: #e0f0ff;
    border-radius: 5px;
  }
}

.box {
  display: flex;
  justify-content: space-between;
  padding: 0 30%;

  .modalBtn {
    cursor: pointer;
    line-height: 40px;
    text-align: center;
    width: 80px;
    height: 40px;
    background: #5585ec;
    border-radius: 4px;
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;

    &:nth-child(2) {
      background: #999;
    }
  }
}
 .foot {
    text-align: center;
    margin: 20px 0px;
    .foots {
      display: inline-block;
      width: 80px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background: #5585ec;
      border-radius: 4px;
      font-family: PingFang SC;
      font-weight: 600;
      color: #ffffff;
      font-size: 14px;
      cursor: pointer;
    }
  }
</style>
