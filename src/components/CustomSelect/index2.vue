<template>
  <div class="custom-select" ref="frame">
    <div class="selecteds" @click="toggleDropdown">
      <div
        v-if="selectedOptions.length"
        class="tags"
        :style="
          wrapFlag
            ? 'flex-wrap: nowrap;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;'
            : ''
        "
      >
        <span class="tag" v-for="option in selectedOptions" :key="option.id">
          {{ option.parentId != 0 ? getParentName(option.parentId) : "" }}
          {{ option.name }}
          <span class="remove" @click.stop="removeOption(option.id)">×</span>
        </span>
      </div>
      <div v-else>
        {{ placeholder }}
      </div>
    </div>
    <div class="dropdown" v-if="isOpen">
      <div class="option" v-for="option in newOptions" :key="option.id">
        <div>
          <div
            @click.stop="toggleOption(option)"
            :class="{ selected: value.includes(option.id) }"
          >
            <span
              @click.stop="addChildren(option.id)"
              v-if="option.children.length > 0"
              ><Icon
                type="ios-arrow-down"
                v-if="!showList.includes(option.id)"
              ></Icon>
              <Icon
                type="ios-arrow-up"
                v-if="showList.includes(option.id)"
              ></Icon
            ></span>
            <span>{{ option.name }}</span>
          </div>
          <div>
            <div
              v-show="showList.includes(option.id)"
              v-for="c in option.children"
              :key="c.id"
              :class="{ selected: value.includes(c.id) }"
              @click.stop="toggleOption(c)"
              style="padding-left: 40px;"
            >
              {{ c.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import cloneDeep from "lodash.clonedeep";
export default {
  name: "CustomSelect",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    options: {
      type: Array,
      required: true,
    },
    placeholder: {
      type: String,
      default: "Please select",
    },
    wrapFlag: {
      default: null,
    },
  },
  data() {
    return {
      isOpen: false,
      selectedOptions: [],
      showList: [],
      newOptions: [],
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.updateSelectedOptions(newVal);
      },
      deep: true,
    },

    options: {
      handler(newVal) {
        this.newOptions = cloneDeep(newVal);
        if (
          (this.$route.path.indexOf("/monitor/recommended") > -1 ||
            this.$route.path.indexOf("/internetReport/reportMaterial") > -1) &&
          this.options.filter((v) => v.name == "未分类").length == 0
        ) {
          this.newOptions.push({ id: "未分类", name: "未分类", children: [] });
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.newOptions = cloneDeep(this.options);
    this.updateSelectedOptions(this.value);
    if (
      (this.$route.path.indexOf("/monitor/recommended") > -1 ||
        this.$route.path.indexOf("/internetReport/reportMaterial") > -1) &&
      this.options.filter((v) => v.name == "未分类").length == 0
    ) {
      this.newOptions.push({ id: "未分类", name: "未分类", children: [] });
    }
    this.addClickOutsideListener()
  },
  beforeDestroy(){
    this.removeClickOutsideListener();
  },
  methods: {
    handleOutsideClick(event) {
      const element = this.$refs.frame;
      // 判断点击是否在元素内部
      if (!element.contains(event.target)) {
        console.log("点击了外边");
        document.removeEventListener("click", this.handleOutsideClick); // 清除监听器
        // this.$emit("outside-click"); // 触发外部点击事件
        this.isOpen = false;
      } else {
        console.log("点击了内部");
      }
    },
    getParentName(d) {
      let list = this.newOptions.filter((v) => v.id == d);
      if (list.length > 0) {
        return list[0].name + "-";
      } else {
        return "";
      }
    },
    addChildren(id) {
      if (this.showList.includes(id)) {
        this.showList.splice(this.showList.indexOf(id), 1);
      } else {
        this.showList.push(id);
      }
    },
    toggleDropdown() {
      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        console.log("添加点击事件");
        document.addEventListener("click", this.handleOutsideClick);
      }
    },
    toggleOption(option) {
      if (option.children.length > 0) {
        return;
      }
      if (this.value.includes(option.id)) {
        this.removeOption(option.id);
      } else {
        this.selectOption(option);
      }
    },
    selectOption(option) {
      if (!this.value.includes(option.id)) {
        //  this.$emit("input", [...this.value, option.id]);
        this.$emit("input", [...this.value, option.id]);
      }
    },
    removeOption(id) {
      this.$emit(
        "input",
        this.value.filter((val) => val !== id)
      );
    },
    updateSelectedOptions(values) {
      if (Array.isArray(this.newOptions)) {
        let list = [];
        this.newOptions.forEach((v) => {
          if (values.includes(v.id)) {
            list.push(v);
          }
          if (v.children && v.children.length > 0) {
            v.children.forEach((c) => {
              if (values.includes(c.id)) {
                list.push(c);
              }
            });
          }
        });
        this.selectedOptions = list;
      } else {
        this.selectedOptions = [];
      }
    },
    handleOutsideClick(event) {
      if (
        !this.$el.contains(event.target)) {
          this.isOpen = false
      }
    },
    // 添加监听事件
    addClickOutsideListener() {
      document.addEventListener("click", this.handleOutsideClick);
    },
    // 移除监听事件
    removeClickOutsideListener() {
      document.removeEventListener("click", this.handleOutsideClick);
    },
  },
};
</script>

<style lang="less" scoped>
.custom-select {
  position: relative;
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  color: #333;
  & > .selecteds {
    padding: 8px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background-color: #fff;
    color: #aaa;

    .tags {
      display: flex;
      flex-wrap: wrap;
    }

    .tag {
      padding: 2px 6px;
      margin: 2px;
      display: flex;
      align-items: center;
      color: #000;
      height: 20px;
      background: #e6e4e4;
      border-radius: 2px;
      font-size: 12px;

      .remove {
        margin-left: 4px;
        cursor: pointer;
      }
    }

    .arrow {
      margin-left: auto;
      transition: transform 0.2s;

      &.open {
        transform: rotate(180deg);
      }
    }
  }

  .dropdown {
    // flex-wrap: wrap;
    position: absolute;
    top: 100%;
    width: 100%;

    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    height: 300px;
    overflow: auto;

    .option {
      margin: 5px 5px 0;
      padding: 0 2px;
      cursor: pointer;
      //width: 54px;
      min-height: 20px;
      //   border: 1px solid #c4c3c3;
      border-radius: 2px;
      font-size: 12px;

      &.selected {
        background-color: #e6f7ff;
      }

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
  .selected {
    background-color: #e6f7ff;
  }
}
</style>
