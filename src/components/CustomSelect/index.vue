<template>
  <div class="custom-select" ref="frame">
    <div class="selected" @click="toggleDropdown">
      <div v-if="selectedOptions.length" class="tags">
        <span class="tag" v-for="option in selectedOptions" :key="option.id">
          {{ option.name }}
          <span class="remove" @click.stop="removeOption(option.id)">×</span>
        </span>
      </div>
      <div v-else>
        {{ placeholder }}
      </div>
    </div>
    <div class="dropdown flex" v-if="isOpen">
      <div
        class="option"
        v-for="option in options"
        :key="option.id"
        @click="toggleOption(option)"
        :class="{ selected: value.includes(option.id) }"
      >
        {{ option.name }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CustomSelect",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    options: {
      type: Array,
      required: true,
    },
    placeholder: {
      type: String,
      default: "Please select",
    },
  },
  data() {
    return {
      isOpen: false,
      selectedOptions: [],
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.updateSelectedOptions(newVal);
      },
      deep: true,
    },

    options() {
      this.updateSelectedOptions(this.value);
    },
  },
  mounted() {
    this.updateSelectedOptions(this.value);
  },
  beforeDestroy() {
    document.removeEventListener("click", this.handleOutsideClick);
  },
  methods: {
    handleOutsideClick(event) {
      const element = this.$refs.frame;
      // 判断点击是否在元素内部
      if (!element.contains(event.target)) {
        console.log("点击了外边");
        document.removeEventListener("click", this.handleOutsideClick); // 清除监听器
        // this.$emit("outside-click"); // 触发外部点击事件
        this.isOpen = false;
      } else {
        console.log("点击了内部");
      }
    },
    toggleDropdown() {
      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        console.log("添加点击事件");
        document.addEventListener("click", this.handleOutsideClick);
      }
    },
    toggleOption(option) {
      if (this.value.includes(option.id)) {
        this.removeOption(option.id);
      } else {
        this.selectOption(option);
      }
    },
    selectOption(option) {
      if (!this.value.includes(option.id)) {
        this.$emit("input", [...this.value, option.id]);
      }
    },
    removeOption(id) {
      this.$emit(
        "input",
        this.value.filter((val) => val !== id)
      );
    },
    updateSelectedOptions(values) {
      if (Array.isArray(this.options)) {
        this.selectedOptions = this.options.filter((option) => {
          return values.includes(option.id);
        });
      } else {
        this.selectedOptions = [];
      }
      console.log(this.selectedOptions);
    },
  },
};
</script>

<style lang="less" scoped>
.custom-select {
  position: relative;
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  flex-direction: column;

  & > .selected {
    padding: 8px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background-color: #fff;
    color: #aaa;

    .tags {
      display: flex;
      flex-wrap: wrap;
    }

    .tag {
      padding: 2px 6px;
      margin: 2px;
      display: flex;
      align-items: center;
      color: #000;
      height: 20px;
      background: #e6e4e4;
      border-radius: 2px;
      font-size: 12px;

      .remove {
        margin-left: 4px;
        cursor: pointer;
      }
    }

    .arrow {
      margin-left: auto;
      transition: transform 0.2s;

      &.open {
        transform: rotate(180deg);
      }
    }
  }

  .dropdown {
    flex-wrap: wrap;
    position: absolute;
    top: 100%;
    width: 100%;

    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;

    .option {
      margin: 5px 5px 0;
      padding: 0 2px;
      cursor: pointer;
      //width: 54px;
      height: 20px;
      border: 1px solid #c4c3c3;
      border-radius: 2px;
      font-size: 12px;

      &.selected {
        background-color: #e6f7ff;
      }

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}
</style>
