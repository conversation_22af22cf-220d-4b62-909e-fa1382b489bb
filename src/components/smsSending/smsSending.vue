<template>
  <div class="newPush_frame">
    <div
      v-for="(d, index) in dataList"
      :key="index"
      :style="{
        right:
          start &&
          (idList.indexOf(d.msgId) > -1 ||
            idList.indexOf(d.id) > -1)
            ? '20px'
            : '-400px',
        bottom: index * 170 + 20 + 'px',
      }"
      class="item_frame"
    >
      <div class="header">
        <div style="display: flex; align-items: center">
          <svg-icon
            v-if="d.msgType == 'evidenceMsg'"
            icon-class="取证-弹框"
            style="width: 24px; height: 18px"
          />
          <img
            v-else
            :src="smsImg"
            alt=""
            style="width: 32px; height: 24px"
          />
          
          <span class="title">
            {{ d.msgType == 'evidenceMsg' ? "取证结果提醒" : "短信发送状态提醒" }}
          </span>
        </div>
        <div>
          <Icon
            class="close"
            title="关闭"
            type="md-close"
            @click="close(index)"
          />
        </div>
      </div>
      <!-- 取证提醒 -->
      <div v-if="d.msgType == 'evidenceMsg'" class="content">
        <span style="width: 16px; margin-right: 6px">
          <svg-icon v-if="d.status == 1" icon-class="短信发送-成功" />
          <svg-icon v-else icon-class="短信发送-失败" />
        </span>
        <div style="display: flex; position: relative; height: 48px">
          <span
            class="ellipsis-2"
            style="min-height: 24px; width: 260px; max-height: 48px"
          >
            信息“{{
              d.status == 1 ? d.evidenceMsgDate.msgTitle + "”取证成功" : d.evidenceMsgDate.msgTitle + "”取证失败"
            }}
          </span>

          <span
            v-if="d.status == 1"
            class="cf"
            style="
              white-space: nowrap;
              height: 25px;
              position: relative;
              bottom: -20px;
            "
            @click="LookEvidence(d.evidenceMsgDate)"
            >查看取证</span
          >
          <span
            v-else
            class="cf"
            style="
              white-space: nowrap;
              height: 25px;
              position: relative;
              bottom: -20px;
            "
            @click="GatherEvidence(d.evidenceMsgDate)"
            >重新取证</span
          >
        </div>
      </div>
      <!-- 短信提醒 -->
      <div v-else style="height: 100px; text-align: center; line-height: 100px">
        <svg-icon v-if="d.status == 0" icon-class="短信发送-成功" />
        <svg-icon v-else icon-class="短信发送-失败" />
        <span>{{ d.message }}</span>
        <span v-if="d.status != 0" class="cf" @click="handleSends(d, index)"
          >重新发送</span
        >
      </div>
    </div>

    <ImgPreview
      v-if="ImgPreviewStatus"
      :imgList="imgList"
      @close="ImgPreviewStatus = false"
    />
  </div>
</template>

<script>
import situations from "@/assets/json/situations.json";
import smsImg from "@/assets/img/sms-tips.png";
import ImgPreview from "@/components/imgPreview/index.vue";

export default {
  name: "index",
  components: { ImgPreview },
  data() {
    return {
      smsImg,
      start: false,
      data: {},
      datas: {
        smsContent: "",
      },
      situations,
      type: "msg",
      dataList: [],
      idList: [],
      lockReconnect: false, //是否真正建立连接
      timeout: 50 * 1000, //58秒一次心跳
      timeoutObj: null, //心跳心跳倒计时
      serverTimeoutObj: null, //心跳倒计时
      timeoutnum: null, //断开 重连倒计时
      timer: null,
      ImgPreviewStatus: false,
      imgList: null,
    };
  },
  methods: {
     //取证
    GatherEvidence(d) {
      if (d.isEvidence === 1) {
        this.$Message.warning("信息正在取证中，请稍后查看");
        return false;
      }
      let params = {
        mkey: d.mkey || d.msgKey, //数据mkey
        type: d.type, //1 涉济，2 涉鲁，3涉政
      };
      this.$http
        .post("/recommend/obtainEvidenceMsg", params, { emulateJSON: true })
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.warning("信息正在取证中，请稍后查看");
            d.isEvidence = 1;
          } else {
            this.$Message.error(res.body.message);
          }
        });
    },
    //图片放大
    blowUp(imgList) {
      this.imgList = imgList;
      this.ImgPreviewStatus = true;
    },
    // 查看取证
    LookEvidence(d) {
      const LookEvidence = () => import("@/components/LookEvidence");
      this.$modal.show({
        component: LookEvidence,
        componentProps: {
          data: d,
          that: this,
          moduleType: {},
        },
        componentEvents: {
          // closes: this.tipCancelModel,
          blowUp: this.blowUp,
        },
        title: "查看取证", // 传递标题
        // y: 300,
      });
    },
    handleSends(d, i) {
      let params = {
        smsContent: d.smsContent, //短信内容
        promptNum: d.promptNum, // 提示单编号
        msgId: d.msgId, // 日志主键id
      };
      this.$http
        .post("/smsSend/promptAgainSms", params, { emulateJSON: true })
        .then((res) => {
          let result = res.body;
          if (result.status == 0) {
            this.close(i);
          }
        });
    },
    close(i) {
      if (this.dataList.length > 1) {
        this.dataList.splice(i, 1);
        // this.idList = this.dataList.map((v) => (v = v.promptNum));
        let list = []
        this.dataList.forEach(v => {
          if (v.msgType == 'sendSms') {
            list.push(v.msgId)
          }else {
           list.push(v.id)
          }
        })
        this.idList = list
        let newData = this.dataList.filter((v) => v.status == 0 && v.msgType == 'sendSms');
        console.log(newData, "newData");
        if (newData.length == 0 || !newData) {
          clearInterval(this.timer);
        }
      } else {
        this.start = false;
        this.dataList = [];
        this.idList = [];
      }
    },
    sendHeard(myws) {
      if (!myws) {
        return;
      }
      let param = {
        userName: localStorage.getItem("userName"),
        userAccount: localStorage.getItem("userAccount"),
        departmentName: localStorage.getItem("departmentName"),
        ip: localStorage.getItem("ip"),
        browser: localStorage.getItem("browser"),
        env: process.env.RUN_MODE == "build" ? "prod" : "test",
      };
      myws.send(JSON.stringify(param)); //这里可以自己跟后端约定
    },
    //webSocket
    getData() {
      let ws = new WebSocket(
        process.env.NEW_PUSH_SOCKET_URL + localStorage.getItem("userAccount")
      );

      ws.onopen = () => {
        //开启心跳
        this.startWs(ws);
        this.sendHeard(ws);
      };
      //链接关闭事件
      ws.onclose = (event) => {
        this.reconnect();
      };
      //获取后端的数据
      ws.onmessage = (data) => {
        
        if (data.data.indexOf("{") == 0) {
          let a = JSON.parse(data.data);
          if (data.data && (a.msgType === "sendSms" || a.msgType === "evidenceMsg")) {
            this.start = true;
            this.dataList.push(a);
            if (a.msgType === "sendSms") {
              this.idList.push(a.msgId);
            }else {
              this.idList.push(a.id);
            }
            
            if (a.status == 0 && a.msgType === "sendSms") {
              let index = "";
              this.dataList.forEach((v, i) => {
                if (v.status == 0 && v.msgType === "sendSms") {
                  return (index = i);
                }
              });
              if (index == 0 || index) {
                this.timer = setTimeout(() => this.close(index), 5 * 1000);
              }
            }
          }
        }
        //收到服务器信息，心跳重置
        this.reset(ws);
      };
      ws.onerror = (data) => {
        //收到服务器信息，心跳重置
        // console.log("链接错误:" + data);
        this.reconnect();
      };
    },

    reconnect() {
      //重新连接
      var that = this;
      // console.log("重新链接lockReconnect:" + that.lockReconnect);
      if (that.lockReconnect) {
        return;
      }
      that.lockReconnect = true;
      //没连接上会一直重连，设置延迟避免请求过多
      that.timeoutnum && clearTimeout(that.timeoutnum);
      that.timeoutnum = setTimeout(function () {
        //新连接
        that.getData();
        that.lockReconnect = false;
      }, 3000);
    },
    reset(myws) {
      //重置心跳
      var that = this;
      //清除时间
      clearTimeout(that.timeoutObj);
      clearTimeout(that.serverTimeoutObj);
      //重启心跳
      that.startWs(myws);
    },
    startWs(myws) {
      //开启心跳
      var self = this;
      self.timeoutObj && clearTimeout(self.timeoutObj);
      self.serverTimeoutObj && clearTimeout(self.serverTimeoutObj);
      self.timeoutObj = setTimeout(function () {
        //这里发送一个心跳，后端收到后，返回一个心跳消息， 长时间没有消息通信，后端会自动断开
        if (myws.readyState == 1) {
          //如果连接正常
          self.sendHeard(myws);
        } else {
          //否则重连
          self.reconnect();
        }
        self.serverTimeoutObj = setTimeout(function () {
          //超时关闭
          myws.close();
        }, self.timeout);
      }, self.timeout);
    },
  },
  beforeDestroy() {
    // 清除定时器，防止内存泄漏
    clearInterval(this.timer);
  },
  mounted() {
    // if (!gl.develop) {
    //本地为了调试方便，不弹窗
    // this.dataList = [];
    // this.idList = [];
    this.getData();
    // }
  },
};
</script>

<style lang="less" scoped>
.item_frame {
  z-index: 10000;
  width: 400px;
  background: #ffffff;
  box-shadow: 0px 4px 12px 0px rgba(9, 11, 30, 0.25);
  border-radius: 4px;
  position: fixed;
  bottom: 20px;
  right: -400px;
  transition: 0.3s;

  .content {
    font-size: 16px;
    width: 380px;
    height: 100px;
    padding: 20px 0px 0px 36px;
    display: flex;
  }

  .header {
    width: 100%;
    height: 50px;
    border-bottom: 1px solid #dfe3ed;
    padding: 0 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    /deep/ .ivu-icon {
      font-size: 24px;
    }

    .title {
      font-family: PingFangSC-Semibold;
      font-size: 14px;
      color: #383f4f;
      font-weight: 600;
    }

    .close {
      cursor: pointer;
      margin: 0 0 0 10px;
      font-size: 18px;
    }
  }

  .cf {
    color: #4070dd;
    border-bottom: 1px solid #4070dd;
    cursor: pointer;
  }
}
</style>
