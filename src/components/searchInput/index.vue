<template>
  <div class="search flex sb">
    <div ref="myElement" class="input" >
      <Input placeholder="请输入您要查找的内容" v-model="keyword" @on-enter="search">
        <svg-icon
          icon-class="提示"
          slot="suffix"
          @click.native="tipsStatus = !tipsStatus"
        />
      </Input>
      <div class="FloatingFrame" v-show="historyAndRuleStatus || tipsStatus">
        <KeywordRule v-if="tipsStatus" />
        <div class="historyList" v-else>
          <div
            class="item flex"
            v-for="item in reversedHistoryList"
            :key="item.id"
          >
            <span class="ellipsis" @click="fillKeyword(item)">
              {{ item.value }}
            </span>
          </div>
          <div class="clear" v-show="historyList.length > 0">
            <span @click="clearHistory">
              清空历史搜索
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="btn" @click="search">
      搜索
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

import KeywordRule from "@/components/keywordRule/index.vue";
import moment from "moment";

export default {
  data() {
    // 这里存放数据
    return {
      historyAndRuleStatus: false,
      tipsStatus: false,
      historyList: [],
      keyword: ""
    };
  },

  // import 引入的组件需要注入到对象中才能使用
  components: { KeywordRule },
  props: {
    historyListName: {
      default: "historyList"
    }
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    if (this.$route.query.keyword) {
      this.keyword = this.$route.query.keyword;
      this.$emit("search", { keyword: this.keyword });
    }
    this.historyList = window.localStorage.getItem(this.historyListName)
      ? JSON.parse(window.localStorage.getItem(this.historyListName))
      : [];
  },
  // 方法集合
  methods: {
    search() {
      this.historyList.push({
        id: moment().valueOf(),
        value: this.keyword
      });
      this.historyList = this.removeDuplicatesByValue(this.historyList);
      window.localStorage.setItem(
        this.historyListName,
        JSON.stringify(this.historyList)
      );
      const query=JSON.parse(JSON.stringify(this.$route.query))
      query.keyword = this.keyword;
      this.$router.push({ path: this.$route.path, query })
      this.$emit("search", { keyword: this.keyword });
    },
    //根据value去重
    removeDuplicatesByValue(items) {
      const uniqueValues = new Set();
      const uniqueItems = [];

      for (const item of items) {
        if (!uniqueValues.has(item.value)) {
          uniqueValues.add(item.value);
          uniqueItems.push(item);
        }
      }

      return uniqueItems;
    },
    // 历史记录回填
    fillKeyword(d) {
      this.keyword = d.value;
    },
    clearHistory() {
      this.historyList = [];
      window.localStorage.removeItem(this.historyListName);
    },
    handleClickOutside(event) {
      this.historyAndRuleStatus = this.$refs.myElement.contains(event.target);
    }
  },
  // 计算属性 类似于 data 概念
  computed: {
    // 计算属性返回倒序数组
    reversedHistoryList() {
      return this.historyList.slice().reverse();
    }
  },
  // 监控 data 中的数据变化
  watch: {
    keyword(d) {}
  },
  //过滤器
  filters: {},
  beforeDestroy() {
    document.removeEventListener("click", this.handleClickOutside);
  },

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    document.addEventListener("click", this.handleClickOutside);
  }
};
</script>

<style scoped lang="less">
.search {
  .input {
    flex: 1;
    position: relative;
  }

  /deep/ .ivu-input {
    height: 60px;
    color: #999999;
    font-size: 16px;
  }

  .svg-icon {
    width: 26px;
    height: 26px;
    margin: 17px 20px 0 0;
    cursor: pointer;
  }

  .btn {
    margin-left: 20px;
    width: 136px;
    height: 60px;
    background: #5585ec;
    border-radius: 4px;
    color: #ffffff;
    font-size: 16px;
    text-align: center;
    line-height: 60px;
    cursor: pointer;
  }

  .FloatingFrame {
    position: absolute;
    top: 60px;
    background: #ffffff;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    width: 896px;
    z-index: 3;

    .keywordRule {
      background-color: #fff;
    }

    .historyList {
      .clear {
        text-align: center;

        span {
          text-decoration: underline;
          line-height: 40px;
          cursor: pointer;
          color: #5585ec;
        }
      }

      .item {
        line-height: 40px;
        font-size: 16px;
        padding: 0 20px;
        align-items: center;

        span {
          cursor: pointer;
          margin-right: 30px;
        }

        /deep/ .ivu-icon {
          font-size: 20px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
