<template>
  <div>
    <div class="header">
      <span class="xian"></span
      ><span class="yuan" style="margin: 0px 24px 0px 3px"></span>
      “不忘初心”信息报送
      <span class="yuan" style="margin: 0px 3px 0px 25px"></span
      ><span class="xians"></span>
    </div>
    <!-- 信息标题 -->
    <div class="content" style="margin-top: 60px">
      <div class="name">信息标题：</div>
      <div class="inputs">
        <Input
          v-model="title"
          placeholder="请输入信息标题"
          style="width: 530px"
        />
      </div>
    </div>

    <div class="content">
      <div class="name" style="display: flex; justify-content: space-between">
        <span>链</span> <span>接：</span>
      </div>
      <div class="inputs">
        <Input
          v-model="contentUrl"
          placeholder="请输入链接"
          style="width: 530px"
        />
      </div>
    </div>

    <!-- 摘要 -->
    <div class="content" style="align-items: flex-start">
      <div
        class="name"
        style="display: flex; justify-content: space-between; margin-top: 7px"
      >
        <span>截</span> <span>图：</span>
      </div>

      <div class="inputs-zhai" @paste="onPaste">
        <Input
          v-model="digest"
          placeholder="粘贴截图"
          type="textarea"
          style="width: 530px"
          v-if="!imgUrl"
        />
        <div
          v-else
          style="
            width: 530px;
            border: 1px solid #3b5576;
            height: 300px;
            border-radius: 4px;
            margin: auto;
          "
        >
          <img
            :src="imgBaseUrl + imgUrl"
            alt=""
            style="width: 430px; height: 240px"
          />
        </div>
      </div>
    </div>

    <!-- 按钮 -->
    <div class="btn" v-if="!data">
      <span style="width: 80px; background: #999999" @click="close">关闭</span>
      <div>
        <span style="margin-right: 20px" @click="submission(0)"
          >报送并关闭页面</span
        >
        <span @click="submission(1)">报送并录入下一条</span>
      </div>
    </div>
    <!-- 按钮 -->
    <div class="btn" v-else style="justify-content: center">
      <span
        style="width: 80px; background: #999999; margin-right: 70px"
        @click="close"
        >关闭界面</span
      >
      <span @click="submission(0)" style="width: 80px">重新报送</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    name: {
      default: "",
    },
    data: {
      default: {},
    },
  },
  watch: {
    data(val) {
      this.datas = val;
      if (val) {
        this.title = val.msgTitle;
        this.contentUrl = val.msgUrl;
        this.digest = val.digest;
        this.fileId = val.fileId;
      } else {
        this.init();
      }
    },
  },
  data() {
    return {
      title: "", //信息标题
      contentUrl: "", //链接地址
      digest: "", //摘要
      datas: null,
      imgUrl: "", //图片地址
      fileId: "",
    };
  },
  methods: {
    onPaste(e) {
      let file = e.clipboardData.items[0];
      if (file.type.includes("image")) {
        let imgFile = file.getAsFile();
        let formData = new FormData();
        formData.append("file", imgFile);
        formData.append("type", 7);
        this.$http.post("/minIO/common/uploadFiles", formData).then((res) => {
          let result = res.body;
          if (result.status === 0) {
            this.imgUrl = result.data.fileUrl;
            this.fileId = result.data.fileId;
          } else {
            this.$Message.error(result.message);
          }
        });
      }
    },
    // 关闭
    close() {
      this.$emit("close");
    },
    init() {
      this.title = "";
      this.contentUrl = "";
      this.fileId = "";
      this.imgUrl = "";
    },
    // 报送并关闭界面
    submission(id) {
      if (this.title == "") {
        return this.$Message.error("请输入信息标题！");
      }
      if (this.contentUrl == "" || this.contentUrl.trim() == "") {
        return this.$Message.error("请输入链接！");
      }
      if (this.fileId == "") {
        return this.$Message.error("请粘贴截图");
      }
      let url = "/harm/report";
      let data = {
        fileId: this.fileId, //视频截图Id
        msgTitle: this.title, //标题
        msgType: this.name == "不忘初心" ? 1 : 2, //信息类型 1 不忘初心 2 牢记使命
        msgUrl: this.contentUrl, //链接
        operationType: this.data ? 1 : 0, //0 入库 1 编辑
      };
      if (this.data) {
        data.reportId = this.data.reportId; //编辑类型时，传上报主键id
        data.markStatus = 0;
      }
      this.$http.post(url, data).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          this.$emit("renewalList");
          if (id == 0) {
            this.close();
          } else {
            this.init();
          }
        } else {
          this.$Message.error(result.message);
        }
      });
    },
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.header {
  font-weight: 600;
  color: #5d5d5d;
  font-size: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 34px 0px 0px 0px;

  .xian {
    display: inline-block;
    width: 83px;
    height: 4px;
    background: linear-gradient(90deg, rgba(85, 133, 236, 0) 0%, #5585ec 100%);
  }

  .xians {
    display: inline-block;
    width: 83px;
    height: 4px;
    background: linear-gradient(90deg, #5585ec 0%, rgba(85, 133, 236, 0) 100%);
  }

  .yuan {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    background: linear-gradient(
      90deg,
      rgba(85, 133, 236, 0.99) 0%,
      #5585ec 100%
    );
  }
}

.content {
  display: flex;
  align-items: center;
  margin: 0px 0px 40px 24px;

  .name {
    width: 80px;
    font-weight: 600;
    color: #5585ec;
    font-size: 16px;
    margin-right: 4px;
  }

  .inputs {
    /deep/ .ivu-input {
      height: 40px;
      border: 1px solid #3b5576;
    }
  }

  .inputs-zhai {
    /deep/ .ivu-input {
      height: 300px;
      border: 1px solid #3b5576;
    }
  }
}

.btn {
  display: flex;
  margin: 80px 116px 155px 108px;
  justify-content: space-between;

  span {
    cursor: pointer;
    display: inline-block;
    width: 120px;
    height: 40px;
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
    background: #5585ec;
    border-radius: 4px;
    text-align: center;
    line-height: 40px;
  }
}
</style>
