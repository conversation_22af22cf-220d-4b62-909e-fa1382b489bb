<!-- 入口页 -->
<template>
  <div style="width: 850px; padding:16px 16px 0px 16px;position: relative;top: -80px;background:#fff;">
    <div style="display: flex; justify-content: center; align-items: center">
      <span
        style="
          margin-right: 10px;
          width: 38px;
          height: 790px;
          padding-top: 390px;
        "
        :class="tipsIndex != 0 ? 'icons-zong' : ''"
      >
        <svg-icon
          icon-class="舆情提示单-翻页"
          style="width: 38px; height: 38px"
          class="icons"
          @click.native="addIndex(1)" /></span
      ><Tipsnew
        :dataItem="t"
        v-for="(t, index) in tipsList"
        :key="t.id"
        v-show="index == tipsIndex"
      />
      <span
        style="
          margin-left: 20px;
          width: 38px;
          height: 790px;
          padding-top: 390px;
        "
        :class="
          tipsIndex + 1 != tipsList.length && tipsList.length > 1
            ? 'icons-zong'
            : ''
        "
      >
        <svg-icon
          icon-class="舆情提示单-翻页"
          class="icons"
          style="width: 38px; height: 38px; transform: rotate(180deg)"
          @click.native="addIndex(2)"
        />
      </span>
    </div>

    <div class="tips-foot">{{ tipsIndex + 1 }}/{{ tipsList.length }}</div>
  </div>
</template>

<script>
import Tipsnew from "./ntips.vue"; //修改查看舆情提示单界面
export default {
  components: {
    Tipsnew,
  },
  data() {
    return {
      tipsIndex: 0,
      tipsList: [],
    };
  },
  //生命周期 - 创建完成（访问当前this实例）
  props: {
    list: {
      default: () => [],
    },
  },
  watch: {
    list: {
      handler(val) {
        this.tipsList = val;
      },
      deep: true,
    },
  },
  created() {},
  //方法所在
  methods: {
    addIndex(d) {
      if (d == 1) {
        this.tipsIndex = this.tipsIndex - 1;
      } else if (d == 2) {
        this.tipsIndex = this.tipsIndex + 1;
      }
    },
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {
    this.tipsList = this.list
  },
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.icons-zong {
  width: 38px;
  height: 38px;
}
.icons-zong:hover {
  .icons {
    display: block;
    cursor: pointer;
  }
}
.icons {
  display: none;
}
.tips-foot {
  font-size: 20px;
  text-align: center;
  padding-top: 20px;
  margin-top: 20px;
  border-top: 2px dashed #dbdbdb;
  
}
</style>
