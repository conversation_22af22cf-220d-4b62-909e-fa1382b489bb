<template>
    <div class="forward-container">
        <!-- 标题 -->
        <!-- <h2 style="margin-bottom: 20px">转发到山东通</h2> -->
        <Row :gutter="20">
            <!-- 左侧Tab内容 -->
            <Col span="12">
                <Select
                    class="select_box"
                    v-model="selectedItems"
                    multiple
                    filterable
                    remote
                    :remote-method="handleSearch"
                    :max-tag-count="0"
                    :loading="loading"
                    @on-change="handleSelectChange"
                    placeholder="搜索"
                    style="width: 80%; margin-bottom: 10px"
                >
                    <Option
                        v-for="item in searchResults"
                        :key="item.organId"
                        :value="item.organId"
                        :label="item.label"
                    >
                        <div
                            class="over_option"
                            @click="handleOptClick(item)"
                        ></div>
                        <Checkbox
                            :key="item.organId"
                            :value="allSelectItem.includes(item.organId)"
                            :disabled="item.disabled"
                        >
                        </Checkbox>
                        {{ item.title }}
                    </Option>
                </Select>
                <Tabs v-model="activeTab">
                    <TabPane :label="renderTab1" name="1">
                        <div class="tab_box">
                           <div class="history_box" v-for="item in recentList" :key="item.organId"   @click="handleOptClick(item)">
                                 <Checkbox
                                    :key="item.organId"
                                    :label="item.organId"
                                    :disabled="item.disabled"
                                    :value="allSelectItem.includes(item.organId)"
                                    style="display: block; margin-bottom: 10px"
                                  
                                >
                                {{ item.title }}
                            </Checkbox>
                           </div>
                        </div>
                    </TabPane>

                    <TabPane :label="renderTab2" name="2">
                        <p class="select_tips">未提供山东通ID的人员暂时无法选择</p>
                        <div class="tab_box">
                            <Tree
                                :data="orgTree"
                                show-checkbox
                                check-directly
                                ref="tree"
                                @on-check-change="handleOrgCheckChange"
                            ></Tree>
                        </div>
                    </TabPane>
                </Tabs>
            </Col>

            <!-- 右侧已选择 -->
            <Col span="12">
                <div class="selected-section">
                    <h4>已选择（{{ allSelectItem.length }}）</h4>
                    <div class="selected-list">
                        <Tag
                            v-for="(item, index) in selectedResult"
                            :key="index"
                            closable
                            @on-close="handleRemove(item)"
                        >
                            <svg-icon
                                icon-class="组织机构(蓝)"
                                v-if="item.depCode"
                            ></svg-icon>
                            {{ item.title }}</Tag
                        >
                    </div>
                </div>
            </Col>
        </Row>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <Button style="margin-right: 10px" @click="handleCancel"
                >取消</Button
            >
            <Button type="primary" @click="handleConfirm">确定</Button>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            activeTab: "1", // 当前激活的Tab
            recentSelected: [], // 最近转发已选
            orgSelected: [], // 按机构选已选
            selectedItems: [], // 已选列表
            oldSelectedItem: [],
            allSelectItem: [],
            loading: false,
            selectHttp: null,
            recentList: [
              
            ],
            orgTree: [
                {
                    title: "通讯录",
                    expand: true,
                    checked: false,
                    depCode: '123',
                    level: 'none',
                    children: [],
                    organName: "通讯录",
                },
                // 其他部门结构...
            ],
            searchQuery: "",
            searchResults: [],
            selectedNodes: [],
            selectedResult: [],
        };
    },
    mounted() {
        this.getOrgTree();
        this.getHistorySelect();
    },
    methods: {
        getOrgTree() {
            this.$http
                .get(process.env.HOME_SERVER + "/sys/new/forwardList")
                .then((res) => {
                    if (res.body.status === 0) {
                        const orgList = [];
                        for (let key in res.body.data) {
                            if(key == 1){
                                const keyObj = {
                                   ...res.body.data[key][0],
                                   level: 'none',
                                   depCode: key,
                                }
                                orgList.push(keyObj);
                            }else {
                                const keyObj = {
                                    title: key == 1 ? "市委网信办" : key == 2 ? "区县网信办" : key == 3 ? "市直单位" : "",
                                    expand: true,
                                    checked: false,
                                    depCode: key,
                                    level: 'none',
                                    children: [...res.body.data[key]],
                                    organName: key == 1 ? "市委网信办" : key == 2 ? "区县网信办" : key == 3 ? "市直单位" : "",
                                }
                                orgList.push(keyObj);
                            }

                           
                        }
                        this.$set(
                            this.orgTree[0],
                            "children",
                            this.traverseTree(
                                orgList,
                                (item, parent, level) => {
                                    // item.title = item.organName;
                                    item.checked = false;
                                    item.expand = false;
                                    !(item.children && item.children.length > 0)
                                        ? (item.children = item.users || [])
                                        : "";
                                    item.depCode && !(item.children && item.children.length > 0) ? item.disabled = true : ''
                                    !item.depCode && !(item.children && item.children.length > 0) && !item.sdtId ? item.disabled = true : ''
                                    item.level = level;
                                    return item;
                                }
                            )
                        );
                    } else {
                        this.$Message.warning(res.body.message);
                    }
                });
        },
        getHistorySelect() {
            this.$http.get("/forward/recentlyList").then((res) => {
                if (res.body.status === 0) {
                    // this.$Message.success(res.body.message);
                    this.recentList = res.body.data || [];
                    this.recentList.forEach((item) => {
                        !item.sdtId ? this.$set(item, 'disabled', true): '';
                    })
                } else {
                    this.$Message.warning(res.body.message);
                }
            });
        },
        traverseTree(nodes, callback, parent = null, level = 0) {
            if (!Array.isArray(nodes)) return;
            nodes.forEach((node) => {
                // 创建副本避免修改原始数据
                const processedNode = callback({ ...node }, parent, level);
                if (processedNode.children) {
                    processedNode.children = this.traverseTree(
                        processedNode.children,
                        callback,
                        processedNode, // 传递当前节点作为子节点的父节点
                        level + 1
                    );
                }
                // 返回处理后的节点
                Object.assign(node, processedNode);
            });
            return nodes;
        },
        findNodePath(nodes, targetId) {
            let path = [];
            traverseTree(nodes, (node, parent, level) => {
                if (node.id === targetId) {
                    // 向上追溯父级
                    let current = node;
                    while (current) {
                        path.unshift(current.id);
                        current = current._parent; // 假设遍历时存储了父级引用
                    }
                }
                node._parent = parent; // 临时存储父级引用
                return node;
            });
            return path;
        },
        mergedSelected(selectedNodes) {
            const selectedMap = new Map(); // 用 Map 存储所有选中节点
            selectedNodes.forEach((node) =>
                selectedMap.set(node.organId, node)
            );
            const result = [];
            // 从最上级开始查找
            const sortedNodes = [...selectedNodes].sort(
                (a, b) => a.level - b.level
            );
            sortedNodes.forEach((node) => {
                this.processNode(node, selectedMap, result);
            });
            // console.log(result);
            this.selectedResult = result;
        },
        processNode(node, selectedMap, result) {
            if (!selectedMap.has(node.organId)) {
                return false;
            }
            result.push(node);
            if (node.children && node.children.length > 0) {
                this.deleteSelect(node.children, selectedMap);
            }
            return false;
        },
        deleteSelect(nodes, selectedMap) {
            if (!Array.isArray(nodes)) return;
            nodes.forEach((node) => {
                if (selectedMap.has(node.organId)) {
                    selectedMap.delete(node.organId);
                }
                if (node.children && node.children.length > 0) {
                    this.deleteSelect(node.children, selectedMap);
                }
            });
        },
        // 最近转发选择事件
        handleRecentCheckChange() {
            this.selectedItems = this.recentList
                .filter((item) => this.recentSelected.includes(item.id))
                .map((item) => ({
                    id: item.id,
                    label: item.label,
                }));
        },
        // 按机构选选择事件
        handleOrgCheckChange(selectedNodes, current) {
            console.log(current)
            if(current){
                var flag = false
                this.traverseTree(current.children, (item) => {
                    if(item.disabled){
                        flag = true
                        this.$set(item, 'checked', false)
                    }
                    if(item.children && item.children.length > 0 && item.children.some((item) => item.disabled)){
                        if(item.title == "二级部门001_0001"){
                            console.log(111111)
                        }
                        flag = true
                        this.$set(item, 'checked', false)
                    }
                    return item
                })
                if(flag){
                    this.$set(current, 'checked', false)
                }
            }
            this.$nextTick(() => {
                selectedNodes = this.$refs.tree.getCheckedNodes()
                this.allSelectItem = selectedNodes.filter((item) => !item.depCode ).map((item) => item.organId);
                this.selectedNodes = selectedNodes;
                this.mergedSelected(selectedNodes);
            })
        },
        dealSelectNode(){
            // const nodes = this.$refs.tree.getCheckedAndIndeterminateNodes().filter((item) => item.level != 'none')
            // console.log(nodes, '节点')
            // const selectedMap = new Map(); // 用 Map 存储所有选中节点
            // nodes.forEach((node) =>
            //     selectedMap.set(node.organId, node)
            // );
            //  // 从最下级开始查找
            // const sortedNodes = [...nodes].sort(
            //     (a, b) => b.level - a.level
            // );
            // sortedNodes.forEach((item) => {
            //     if(item.children && item.children.length > 0){
            //         if(item.children.every((subitem) => this.allSelectItem.includes(subitem.organId))){
            //             this.$set(item, 'checked', true)
            //         }
            //     }
            // })
            // console.log(sortedNodes)
            // console.log(this.$refs.tree.getCheckedNodes(), '处理')
            this.$forceUpdate()
            this.$nextTick(() => {
                this.handleOrgCheckChange()
            })
        },
        // 移除已选
        handleRemove(item) {
            const ids = [item.organId]
            this.findChildid(item.children, ids)
            ids.forEach((item) => {
                if(this.allSelectItem.indexOf(item) > - 1){
                     this.allSelectItem.splice(
                      this.allSelectItem.indexOf(item),
                      1
                  )
                }
               
            })
            this.$nextTick(() => {
                this.changeTree();
            });
        },
        findChildid(list, ids){
            if(list && list.length > 0){
                list.forEach((item) => {
                    ids.push(item.organId)
                    this.findChildid(item.children, ids)
                })
            }
        },
        // 取消
        handleCancel() {
            this.$emit('close')
        },
        // 确定
        handleConfirm() {
            const params = this.selectedNodes.filter((item) => {
                return !item.depCode && !!item.sdtId
            }).map((item) => {
                return {
                    sdtId: item.sdtId,
                    title: item.title,
                    organId: item.organId,
                }
            })
            this.$emit('confirm', params)
        },
        handleSearch(val) {
            if (val.trim() === "") {
                this.searchResults = [];
                return;
            }
            this.loading = true;
            this.selectHttp = this.$http
                .get(process.env.HOME_SERVER + "/sys/new/listByKeyWord", {
                    params: {
                        keyWord: val.trim(),
                    },
                })
                .then((res) => {
                    if (res.body.status === 0) {
                        this.searchResults = res.body.data || [];
                        this.searchResults.forEach((item) => {
                            !item.sdtId ? this.$set(item, 'disabled', true): '';
                        })
                    } else {
                        this.searchResults = [];
                        this.$Message.warning(res.body.message);
                    }
                    this.loading = false;
                });
        },
        renderTab1(h) {
            return h("div", [
                this.activeTab == "1"
                    ? h("svg-icon", {
                          props: {
                              "icon-class": "时钟(蓝)",
                          },
                      })
                    : h("svg-icon", {
                          props: {
                              "icon-class": "时钟(灰)",
                          },
                      }),
                h("span", "最近转发"),
            ]);
        },
        renderTab2(h) {
            return h("div", [
                this.activeTab == "2"
                    ? h("svg-icon", {
                          props: {
                              "icon-class": "组织机构(蓝)",
                          },
                      })
                    : h("svg-icon", {
                          props: {
                              "icon-class": "组织机构(灰)",
                          },
                      }),
                h("span", "按机构选"),
            ]);
        },
        handleSelectChange(val) {
            // this.allSelectItem = [...this.allSelectItem,...val]
            // this.traverseTree(this.orgTree[0].children,(item) => {
            //     if(val.includes(item.organId)){
            //         this.$set(item, 'checked', true)
            //     }else{
            //         this.$set(item, 'checked', false)
            //     }
            //     return item
            // })
        },
        handleOptClick(item) {
            if(item.disabled){
                return
            }
            this.allSelectItem.indexOf(item.organId) > -1
                ? this.allSelectItem.splice(
                      this.allSelectItem.indexOf(item.organId),
                      1
                  )
                : this.allSelectItem.push(item.organId);
            this.$nextTick(() => {
                this.changeTree();
            });
        },
        changeTree() {
            this.traverseTree(this.orgTree[0].children, (item) => {
                if ((!item.children || item.children.length == 0)) {
                    if(this.allSelectItem.includes(item.organId)){
                        this.$set(item, "checked", true);
                    }else{
                        this.$set(item, "checked", false);
                    }
                } else if(item.children && item.children.length > 0){
                    if(item.children.every((subitem) => this.allSelectItem.includes(subitem.organId))){
                        this.$set(item, 'checked', true)
                    }else{
                        this.$set(item, 'checked', false)
                    }
                }
                return item;
            });
            this.$nextTick(() => {
               this.dealSelectNode()
            })
        },
    },
    computed: {},
};
</script>

<style scoped lang="less">
.forward-container {
    padding: 20px;
    width: 1000px;
}

.selected-section {
    padding: 20px;
    border-radius: 4px;
}

.selected-list {
    margin: 15px 0;
    max-height: 400px;
    overflow-y: auto;
}

.action-buttons {
    text-align: right;
    margin-top: 20px;
}
.select_tips{
    color: #ccc;
}
.tab_box {
    height: 300px;
    overflow-y: auto;
    .history_box{
        position: relative;
        cursor: pointer;
        &::after{
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            z-index: 99;
           
        }
    }
}
.select_box {
    .ivu-select-item {
        position: relative;
         color: #333 !important;
        &::after{
            display: none !important;
        }
    }
    .over_option {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 99;
    }
    /deep/.ivu-tag.ivu-tag-checked {
        display: none !important;
    }
}
/deep/.ivu-tree ul{
    font-size: 14px !important;
}
/deep/.ivu-checkbox-wrapper{
    font-size: 14px !important;
}
</style>
