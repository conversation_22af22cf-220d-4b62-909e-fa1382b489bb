<!-- 入口页 -->
<template>
<div style="width:1080px;padding:16px;">
  <div style="display:flex;">
    <div class="sendBack">
        <span class="sendBack-title inBlock">退回原因：</span>
        <div class="inputs">
          <Input
            v-model="params.backContent"
            placeholder=""
            type="textarea"
            style="width: 609px"
          />
        </div>
      </div>
       <div style="margin-left: 55px">
      <div class="content-title">
        短信内容：
      </div>

      <div class="dx">
          <div>提示单编号：{{params.promptNum}}</div>
          <div style="line-height:26px;">退回原因：{{params.backContent}}</div>
          <div style="margin-top:5px;">【请及时在系统内或本短信回复反馈情况，反馈内容控制在70字以内】</div>
      </div>
      <div style="margin-top:15px;display:flex;font-size:16px;align-items:center;">
        <svg-icon icon-class="提示单-选中" style="width:17px;height:17px;" @click.native="handleSendFlags(1)" v-if="sendFlags" class="cp" />
        <span class="ckBox" @click="handleSendFlags(1)" v-else></span>
        <span style="margin-left:10px;">使用短信同步下达提示单</span></div>

        <div style="margin-top:15px;display:flex;font-size:16px;align-items:center;">
        <svg-icon icon-class="提示单-选中" style="width:17px;height:17px;" @click.native="handleSendFlags(2)" v-if="sendFlagPeoples" class="cp" />
        <span class="ckBox" @click="handleSendFlags(2)" v-else></span>
        <!-- <span class="ckBox"></span> -->
        <span style="margin:0px 10px;">短信同步发送至</span>
        <span v-if="!sendFlagPeopleEdit" style="margin-right:10px;">"{{params.promptUserPhone}}"</span>
         <Input
              v-else
              v-model="params.promptUserPhone"
              placeholder=""
              type="text"
              style="width: 100px;margin-right:10px;"
            />
        <svg-icon icon-class="提示单-勾选确认" class="cp" style="width:16px;height:16px;margin-top:3px;" v-if="sendFlagPeopleEdit" @click.native="handleSendFlags(3)" />
        <svg-icon icon-class="监测列表-编辑" class="cp" style="width:16px;height:16px;margin-top:3px;" v-else @click.native="handleSendFlags(3)"  />
        
        </div>


    </div>
    <div class="xian"></div>
  </div>
   <div class="foot">
      <span class="foots" @click="submit()">确定</span>
      <span
        class="foots"
        style="margin-left: 70px; background: #999999"
        @click="close()"
        >关闭</span
      >
    </div>
  </div>
</template>

<script>

export default {
  data() {
    return {
        params:{
            promptUserPhone:localStorage.getItem('iphone'),
        },
        sendFlags:true,
        sendFlagPeoples:true,
        sendFlagPeopleEdit:false,
        
    };
  },
  //生命周期 - 创建完成（访问当前this实例）
  props:{
    data:{
        default:() => {}
    }
  },
  watch:{
   data(val) {
    this.params = val
   },
  },
  created() { },
  //方法所在
  methods: {
     submit(){
      let params = this.getParams()
       if (!params.backContent) {
          return this.$Message.error("请输入退回原因");
        }
      this.$emit('handleBack',params)
      this.close()
    },
    close(){
      this.$emit('close')
    },
    getParams() {
        let params = {
          isSendSmsOld:this.sendFlags ? '是':'否',
          isSendUserSmsOld:this.sendFlagPeoples ? '是':'否',
          ...this.params
        }
        return params
    },
     handleSendFlags(type) {
      if (type == 1) {
        this.sendFlags = !this.sendFlags
      }  else if(type == 3){
        this.sendFlagPeopleEdit = !this.sendFlagPeopleEdit
      } else {
        this.sendFlagPeoples = !this.sendFlagPeoples
      }
    },
    
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {
    this.params = this.data
    this.params.backContent = ''
  },
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.sendBack {
  display: flex;
  margin-bottom: 12px;

  .sendBack-title {
    width: 80px;
    color: #5585ec;
    font-size: 16px;
    font-family: PingFang SC;
    margin-top: 7px;
  }

  .inputs {
    /deep/ .ivu-input {
      height: 310px;
      border: 1px solid #999999;
    }
  }
}
.dx {
  width: 300px;
  height: 200px;
  overflow-y: auto;
  border: 1px solid #d0d0d0;
  background: #f2f2f2;
  border-radius: 4px;
  margin-top: 10px;
  padding: 20px 30px 0px 20px;
  font-size: 16px;
  color: #929292;
  .xs {
     display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 4; /* 控制显示的行数 */
  }
 
}
 .ckBox {
    // display: inline-block;
    width: 17px;
    height: 17px;
    background: #fff;
    border: 2px solid #5585ec;
    cursor: pointer;
    border-radius: 2px;
  }
  .content-title {
    width: 80px;
    color: #5585ec;
    font-size: 16px;
    font-family: PingFang SC;
    margin-top: 7px;
    text-align: right;
  }
  .foot {
    text-align: center;
    margin: 20px 0px;
    .foots {
      display: inline-block;
      width: 80px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background: #5585ec;
      border-radius: 4px;
      font-family: PingFang SC;
      font-weight: 600;
      color: #ffffff;
      font-size: 14px;
      cursor: pointer;
    }
  }
</style>
