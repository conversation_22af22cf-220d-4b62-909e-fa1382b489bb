<!-- 入口页 -->
<template>
  <div class="contents">
    <p class="title">舆情提示单</p>
    <p class="nubmer">（编号:{{dataItem.msgNum}}）</p>
    <div class="item-zong">
      <span class="inLock item fw">发送单位</span>
      <span class="inLock item" style="width: 240px">{{dataItem.receiveName}}</span>
      <span class="inLock item fw">发送时间</span>
      <span
        class="inLock item"
        style="border-right: 1px solid #707070; width: 220px"
      >{{dataItem.createTime}}</span>
    </div>

    <div class="item-zong">
      <span class="inLock item fw bt">接收人</span>
      <span class="inLock item bt" style="width: 240px">{{dataItem.receivePersonnel}}</span>
      <span class="inLock item fw bt">联系电话</span>
      <span
        class="inLock item bt"
        style="border-right: 1px solid #707070; width: 220px"
      >{{dataItem.receivePhone}}</span>
    </div>

    <div class="item-zong">
      <span class="inLock item fw bt" style="height: 260px">
        <span class="inLock" style="width:18px;line-height:24px;margin-top:78px;">舆情线索</span></span
      ><span
        class="inLock item bt"
        style="border-right: 1px solid #707070; width: 570px; height: 260px;overflow-y: auto;padding:20px 0px 0px 10px;"
      >
      <p style="line-height:28px;text-align:left;">{{dataItem.msgAbstract}}</p>
      <p style="line-height:28px;text-align:left;">{{dataItem.msgContentUrl}}</p></span>
    </div>

    <div class="item-zong">
      <span class="inLock item fw bt" style="height: 70px">
        <span class="inLock" style="width:54px;line-height:32px;">舆情处置建议</span></span
      ><span
        class="inLock item bt"
        style="border-right: 1px solid #707070; width: 570px; height: 70px;text-align:left;padding-left:10px;overflow-y: auto;line-height:28px;"
      >{{dataItem.suggest}}</span>
    </div>

     <div class="item-zong">
      <span class="inLock item fw bt" style="height: 226px">
        <span class="inLock" style="width:18px;line-height:24px;margin-top:72px;">情况反馈</span></span
      ><span
        class="inLock item bt"
        style="border-right: 1px solid #707070; width: 570px; height: 226px;text-align:left;padding-left:10px;overflow-y: auto;line-height:28px;"
      >{{dataItem.backContent}}</span>
    </div>

    <p style="margin-top:26px;font-size:18px;"><span>单位：{{dataItem.organName}}</span> <span>联系人：{{dataItem.createUserName}}</span> <span>电话：{{dataItem.createUserPhone}}</span></p>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    dataItem: {
      default: () => {},
    },
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {},
  //方法所在
  methods: {},
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {},
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.contents {
  text-align: center;
  font-family: PingFang SC;
  color: #333333;
  position: relative;
  top: -80px;
  background: #fff;
  .title {
    height: 33px;
    font-weight: 600;
    font-size: 24px;
    text-align: center;
    margin-top: 9px;
  }
  .nubmer {
    font-size: 20px;
    margin-bottom: 10px;
  }
  .item-zong {
    display: flex;
    font-size: 18px;
    margin-left: 9px;
    .item {
      width: 110px;
      height: 50px;
      line-height: 50px;
      border: 1px solid #707070;
      border-right: 0px;
    }
  }
}
.inLock {
  display: inline-block;
}
.fw {
  font-weight: 600;
}
.bt {
  border-top: 0px !important;
}
</style>
