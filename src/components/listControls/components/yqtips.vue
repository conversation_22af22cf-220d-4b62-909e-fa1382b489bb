<!-- 入口页 -->
<template>
  <div style="width: 1080px; padding: 16px;">
    <div style="display: flex; position: relative; z-index: 1;">
      <div>
        <!-- 编号 -->
        <div class="content">
          <span class="content-title inBlock">编号：</span>
          <div class="content-number">
            <Input
              v-model="promptNum"
              placeholder="请输入编号"
              type="text"
              style="width: 260px;"
            />
          </div>
        </div>
        <!-- 发往单位、发送时间 -->
        <div style="display: flex; margin-top: -10px;">
          <!-- 发往单位 -->
          <div class="content">
            <span class="content-title inBlock">发往单位：</span>
            <div class="content-number unitFrame">
              <Select
                ref="unitRefs"
                v-model="sendOrgId"
                style="width: 260px;"
                @on-change="changeInstitution"
                @on-open-change="openChange"
              >
                <Option
                  v-for="item in unitList"
                  :value="item.organId"
                  :key="item.organId"
                  >{{ item.organName }}
                </Option>
              </Select>
              <SentUnit
                v-if="SentUnitStatus"
                :allList="unitList"
                @outside-click="SentUnitStatus = false"
                @confirm="changeInstitution"
              />
            </div>
          </div>

          <!-- 发送时间 -->
          <div class="content" style="margin-left: 10px;">
            <span class="content-title inBlock">发送时间：</span>
            <div class="content-number">
              <Input
                v-model="params.sendTime"
                placeholder="请输入关键字"
                type="text"
                style="width: 260px;"
                disabled
              />
            </div>
          </div>
        </div>

        <!-- 接收人、联系电话 -->
        <div style="display: flex; margin-top: -10px;">
          <!-- 接收人 -->
          <div class="content">
            <span class="content-title inBlock">接收人：</span>
            <div class="content-number">
              <Select
                v-model="yqReceiver"
                style="width: 260px;"
                @on-change="changeyqReceiver"
              >
                <Option
                  v-for="item in receiverList"
                  :value="item.userId"
                  :key="item.userId"
                  >{{ item.userName }}
                </Option>
              </Select>
            </div>
          </div>

          <!-- 联系电话 -->
          <div class="content" style="margin-left: 10px;">
            <span class="content-title inBlock">联系电话：</span>
            <div class="content-number">
              <Input
                v-model="sendPhone"
                placeholder="请输入联系电话"
                type="text"
                style="width: 260px;"
              />
            </div>
          </div>
        </div>

        <!-- 舆情线索 -->
        <div class="content" style="margin-top: 0px;">
          <span class="content-title inBlock">舆情线索：</span>
          <div class="old-textNew">
            <p class="old-textNews">
              <Input
                @on-change="changeInput"
                v-model="params.msgAbstract"
                placeholder=""
                type="textarea"
                style="width: 610px;"
                :maxlength="lastLength"
              />
            </p>

            <!-- <p style="padding:10px;max-height:170px; overflow-y: auto;">{{params.msgAbstract}}</p> -->

            <p class="news">
              <Input
                @on-change="changeInputUrl"
                v-model="params.msgContentUrl"
                placeholder=""
                type="text"
                style="width: 610px;"
                :maxlength="Number(1000)"
              />
            </p>
          </div>
        </div>

        <!-- 处置建议 -->
        <div class="content" style="margin-top: 0px;">
          <span class="content-title inBlock">处置建议：</span>
          <div class="content-number">
            <AutoComplete
              v-model="params.suggest"
              @on-change="changeInput"
              :data="suggestList"
              @on-search="handleSearch"
              placeholder=""
              style="width: 609px;"
            >
              <div style="width: 609px;">
                <Option
                  style="
                    width: 100%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  "
                  v-for="(item, index) in suggestList"
                  :value="item"
                  :key="index"
                  :title="item"
                >
                  <span>{{ item }}</span>
                </Option>
              </div>
            </AutoComplete>
            <!-- <Input
              @on-change="changeInput"
              v-model="params.suggest"
              placeholder=""
              type="text"
              style="width: 609px;"
            /> -->
          </div>
        </div>
        <!-- 间隔线 -->
        <p class="interval"></p>

        <!-- 单位 -->
        <div class="content" style="margin-top: 0px;">
          <span class="content-title inBlock">单位：</span>
          <div class="content-number">
            <Input
              v-model="params.yqUnits"
              placeholder=""
              type="text"
              style="width: 609px;"
              disabled
            />
          </div>
        </div>

        <!-- 联系人、电话 -->
        <div style="display: flex; margin-top: -10px; margin-bottom: -18px;">
          <!-- 接收人 -->
          <div class="content">
            <span class="content-title inBlock">联系人：</span>
            <div class="content-number">
              <Select
                v-model="promptUserId"
                style="width: 260px;"
                @on-change="changeyqContactPerson"
              >
                <Option
                  v-for="item in personList"
                  :value="item.userId"
                  :key="item.userId"
                  >{{ item.userName }}
                </Option>
              </Select>
            </div>
          </div>

          <!-- 联系电话 -->
          <div class="content" style="margin-left: 10px;">
            <span class="content-title inBlock">电话：</span>
            <div class="content-number">
              <Input
                v-model="promptUserPhone"
                placeholder=""
                type="text"
                style="width: 260px;"
              />
            </div>
          </div>
        </div>
      </div>
      <div style="margin-left: 55px;">
        <div class="content-title">
          短信内容：
        </div>

        <div class="dx">
          <div>您收到一条新消息待处理（编号：{{ promptNum }}）</div>
          <div style="line-height: 26px;">
            {{ params.msgTitle }}
          </div>
          <!-- <div style="line-height: 26px;">
            舆情线索：{{ params.msgAbstract }}
          </div> -->
          <div style="margin-top: 5px;">{{ params.msgContentUrl }}</div>
          <!-- <div style="margin-top: 5px;">处置建议：{{ params.suggest }}</div>
          <div style="margin-top: 5px;">
            【请及时在系统内或本短信回复反馈情况，反馈内容控制在70字以内】
          </div> -->
        </div>
        <div
          style="
            margin-top: 15px;
            display: flex;
            font-size: 16px;
            align-items: center;
          "
        >
          <svg-icon
            icon-class="提示单-选中"
            style="width: 16px; height: 16px;"
            @click.native="handleSendFlags(1)"
            v-if="sendFlags"
            class="cp"
          />
          <span class="ckBox" @click="handleSendFlags(1)" v-else></span>
          <span style="margin-left: 10px;">使用短信同步下达提示单</span>
        </div>

        <div
          style="
            margin-top: 15px;
            display: flex;
            font-size: 16px;
            align-items: center;
          "
        >
          <svg-icon
            icon-class="提示单-选中"
            style="width: 16px; height: 16px;"
            @click.native="handleSendFlags(2)"
            v-if="sendFlagPeoples"
            class="cp"
          />
          <span class="ckBox" @click="handleSendFlags(2)" v-else></span>
          <!-- <span class="ckBox"></span> -->
          <span style="margin: 0px 10px;">短信同步发送至</span>
          <span v-if="!sendFlagPeopleEdit" style="margin-right: 10px;"
            >"{{ promptUserPhoneNew }}"</span
          >
          <Input
            v-else
            v-model="promptUserPhoneNew"
            placeholder=""
            type="text"
            style="width: 100px; margin-right: 10px;"
          />
          <svg-icon
            icon-class="提示单-勾选确认"
            class="cp"
            style="width: 16px; height: 16px; margin-top: 3px;"
            v-if="sendFlagPeopleEdit"
            @click.native="handleSendFlags(3)"
          />
          <svg-icon
            icon-class="监测列表-编辑"
            class="cp"
            style="width: 16px; height: 16px; margin-top: 3px;"
            v-else
            @click.native="handleSendFlags(3)"
          />
        </div>
      </div>
      <div class="xian"></div>
    </div>

    <div class="foot">
      <span class="foots" @click="submit()">确定</span>
      <span
        class="foots"
        style="margin-left: 70px; background: #999999;"
        @click="close()"
        >暂存并关闭</span
      >
    </div>
  </div>
</template>

<script>
import moment from "moment";
import SentUnit from "./SentUnit.vue";
export default {
  data() {
    return {
      msgTitle: "",
      SentUnitStatus: false,
      sendFlags: true,
      sendFlagPeoples: true,
      sendFlagPeopleEdit: false,
      pathLists: {},
      datas: {},
      params: {
        sendTime: moment(new Date()).format("YYYY-MM-DD"), //舆情提示单发送时间
      },
      sendOrgId: "",
      sendPhone: "",
      yqReceiver: "",
      promptNum: "", //编号
      promptUserPhone: localStorage.getItem("iphone")
        ? localStorage.getItem("iphone")
        : "",
      promptUserPhoneNew: localStorage.getItem("iphone")
        ? localStorage.getItem("iphone")
        : "",
      promptUserId: Number(localStorage.getItem("userId")),
      unitList: [], //舆情提示单发往单位列表
      receiverList: [], //舆情提示单接收人列表
      personList: [], //舆情提示单联系人列表
      absLength: null,
      lastLength: null,
      suggestList: [
        "请关注舆情，及时核查舆情信息，6小时内反馈情况。信息核查属实的，如实反馈线下情况和工作措施。信息核查不属实的，会同属地公安部门落地查人，处置虚假信息，并按时反馈线下实际情况以及发帖人信息和虚假信息处置情况（24小时处置未完成可续报处置情况）。",
        "请关注舆情，及时核查舆情信息，24小时内反馈情况。信息核查属实的，如实反馈线下情况和工作措施。信息核查不属实的，会同属地公安部门落地查人，处置虚假信息，并按时反馈线下实际情况以及发帖人信息和虚假信息处置情况（24小时处置未完成可续报处置情况）。",
        "请关注舆情，妥善做好线下工作，请回复收到。",
      ],
    };
  },
  components: {
    SentUnit,
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {
    this.datas = this.data;
  },
  //方法所在
  methods: {
    handleSearch() {
      return this.suggestList;
    },
    openChange(status) {
      console.log(status);
      this.SentUnitStatus = true;
      document.body.click();
    },
    moment,
    submit() {
      this.$emit("handleYq", this.handleParams());
    },
    close() {
      this.$emit("closes", this.handleParams("add"));
    },
    handleSendFlags(type) {
      if (type == 1) {
        this.sendFlags = !this.sendFlags;
      } else if (type == 3) {
        this.sendFlagPeopleEdit = !this.sendFlagPeopleEdit;
      } else {
        this.sendFlagPeoples = !this.sendFlagPeoples;
      }
    },
    changeInputUrl() {
      this.absLength = this.params.msgContentUrl.length;
      this.lastLength = 1000 - this.absLength;
    },
    changeInput(e) {
      this.$forceUpdate();
    },
    handleParams(type) {
      let params = {
        msgTitle: this.datas.mtitle
          ? this.datas.mtitle
          : "" || this.datas.msgTitle
          ? this.datas.msgTitle
          : "", //标题
        msgContent:
          this.datas.mcontent || this.datas.msgText || this.datas.msgContent, // 正文内容
        msgId: this.datas.mkey || this.datas.msgKey || this.datas.msgId, // 来源模块对应id
        msgType:
          this.that.$route.path.indexOf("monitor/shortVideo") > -1 ? 2 : 1, // 信息类型（1:文本,2:视频）
        // isImportant: this.datas.isImportant, // 是否重点提示（0:否,1:是）
        promptNum: this.promptNum, // 提示单编号
        msgAbstract: this.params.msgAbstract, // 摘要
        msgContentUrl: this.params.msgContentUrl, //舆情线索链接
        suggest: this.params.suggest, // 处置建议
        sendTime: this.params.sendTime, // 下发时间
        sendOrgId: this.sendOrgId, // 下发单位id
        sendPhone: this.sendPhone, // 下发联系方式
        promptOrganName: localStorage.getItem("departmentName"), // 接收单位名称
        promptOrganId: localStorage.getItem("departmentId"), // 提示单联系人id
        promptUserPhone: this.promptUserPhone, // 提示单联系方式
        publishTime:
          moment(this.datas.mpublishTime).format("YYYY-MM-DD HH:mm:ss") ||
          moment(this.datas.publishTime).format("YYYY-MM-DD HH:mm:ss"),
        msgSiution: this.datas.situation || this.datas.msgSiution,
        msgUname:
          this.datas.uname || this.datas.msgUname
            ? this.datas.uname || this.datas.msgUname
            : null,
        isSendSms: this.sendFlags ? "1" : "0",
        isSendUserSms: this.sendFlagPeoples ? "1" : "0",
        sendUserPhone: this.promptUserPhoneNew,
        smsContent: "",
        // +
        // "#处置建议：" +
        // this.params.suggest +
        // "#【请及时在系统内或本短信回复反馈情况，反馈内容控制在70字以内】",
      };
      params.smsContent =
        "您收到一条新消息待处理（编号：" +
        this.promptNum +
        "）#" +
        this.params.msgTitle +
        "#" +
        this.params.msgContentUrl;
      if (type) {
        params.yqReceiver = this.yqReceiver;
        params.receiverList = this.receiverList;
      }

      if (this.sendOrgId) {
        params.sendOrgName = this.getItem(
          this.sendOrgId,
          this.unitList
        )[0].organName; // 下发单位
      }

      if (this.yqReceiver) {
        (params.sendUserName = this.getItem(
          this.yqReceiver,
          this.receiverList
        )[0].userName)
          ? this.getItem(this.yqReceiver, this.receiverList)[0].userName
          : "", // 下发联系人姓名
          (params.sendUserId = this.getItem(
            this.yqReceiver,
            this.receiverList
          )[0].userId
            ? this.getItem(this.yqReceiver, this.receiverList)[0].userId
            : "");
      }

      if (this.promptUserId) {
        let list = this.personList.filter((v) => v.userId == this.promptUserId);
        params.promptUserName = list[0] ? list[0].userName : ""; // 提示单联系人姓名
        params.promptUserId = list[0] ? list[0].userId : "";
      }
      if (
        this.that.$route.path.indexOf("main/publicOpinionTips") > -1 ||
        this.that.$route.query.sourceName === "舆情提示"
      ) {
        params.moduleType = this.datas.moduleType;
        params.moduleSecondType = this.datas.moduleSecondType;
      } else {
        if (!this.pathLists[this.that.$route.query.sourcePath]) {
          params.moduleType = 1;
          params.moduleSecondType = 1;
        } else {
          params.moduleType = this.that.$route.query.sourcePath
            ? this.pathLists[this.that.$route.query.sourcePath].moduleType
            : this.pathLists[this.that.$route.path].moduleType; // 来源模块类型（1:涉济监测,2:涉鲁涉政监测）
          params.moduleSecondType = this.that.$route.query.sourcePath
            ? this.pathLists[this.that.$route.query.sourcePath].moduleSecondType
            : this.pathLists[this.that.$route.path].moduleSecondType; // 来源二级模块类型（1:涉济推荐,2:涉济信息,3:涉济短视频,4:涉济热榜,5:国内热榜,6:涉济报送）
        }
      }
      return params;
    },
    // 获取提示单编号
    getYqNubmer() {
      let url = "/prompt/getPromptNum";
      let params = {};
      this.that.$http.get(url, { params }).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          // msgNum：当前最大编号
          // insertMsgNum：递增过的新增默认编号
          this.promptNum = result.data.insertMsgNum;
          console.log(this.promptNum, "this.promptNum");
        } else {
          this.$Message.error(result.message);
        }
      });
    },
    // 获取部门列表
    getSectionList(id) {
      let params = {
        organId: 0,
      };
      this.that.$http.get("/common/findDept", { params }).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          let list = [
            ...(result.data[2] ? result.data[2] : []),
            ...(result.data[3] ? result.data[3] : []),
          ];
          this.unitList = list;
          if (id) {
            this.getContacts(localStorage.getItem("departmentId"));
            this.getInstitution(this.sendOrgId);
          }
        } else {
          this.$Message.error(result.message);
        }
      });
    },
    // 获取联系人列表
    getContacts(id) {
      let list = [{ depCode: localStorage.getItem("depCode") }];
      let url = "/extApi/findDeptUser";
      if (list.length == 0) {
        return;
      }
      let params = {
        extContent: list[0].depCode,
        organId: id,
      };
      this.getMenhu(url, params).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          this.personList = result.data;
        } else {
          this.$Message.error(result.message);
        }
      });
    },
    // 获取接收人列表
    getInstitution(id) {
      let list = this.unitList.filter((v) => v.organId == id);
      let url = "/extApi/findUserByOrganId";
      if (list.length == 0) {
        return;
      }
      let params = {
        extContent: list[0].depCode,
        organId: id,
      };
      this.getMenhu(url, params).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          this.receiverList = result.data;
        } else {
          this.$Message.error(result.message);
        }
      });
    },
    getMenhu(url, params) {
      return this.that.$http.get(process.env.HOME_SERVER + url, { params });
    },
    // 点击接收单位
    changeInstitution(val) {
      this.sendOrgId = val;
      this.yqReceiver = "";
      this.getInstitution(val);
    },
    // 接收人
    changeyqReceiver(val) {
      this.yqReceiver = val;
      this.sendPhone = this.getItem(val, this.receiverList)[0]
        ? this.getItem(val, this.receiverList)[0].userTelephone
        : null;
    },

    // 联系人联系方式
    changeyqContactPerson(val) {
      this.promptUserId = val;
      this.promptUserPhone = this.getItem(
        val,
        this.personList
      )[0].userTelephone;
    },
    // 获取联系人方式及id

    getItem(d, data) {
      let list = data.filter((v) => v.organId == d || v.userId == d);
      return list.length > 0 ? list : "";
    },
  },
  props: {
    data: {
      default: () => {},
    },
    pathList: {
      default: () => {},
    },
    that: {},
    title: "",
  },
  watch: {
    data: {
      handler(val) {
        // this.datas={};
        // this.sendOrgId= "",
        // this.sendPhone= "",
        // this.yqReceiver= "",
        // this.params.suggest = "",
        console.log(val, "88989------");
        this.datas = val;
        this.params.msgContentUrl = this.datas.murl
          ? this.datas.murl
          : this.datas.msgUrl
          ? this.datas.msgUrl
          : this.datas.msgContentUrl
          ? this.datas.msgContentUrl
          : "";

        this.params.msgTitle = this.datas.mtitle
          ? this.datas.mtitle
          : "" || this.datas.msgTitle
          ? this.datas.msgTitle
          : "";

        this.params.msgAbstract = this.datas.mabstract
          ? this.datas.mabstract
          : this.datas.msgAbstract
          ? this.datas.msgAbstract
          : "";
        if (this.params.msgAbstract.length - 1000 > 0) {
          this.params.msgAbstract = this.params.msgAbstract.slice(
            0,
            1000 - this.params.msgContentUrl.length
          );
        }
        this.lastLength = 1000 - this.params.msgContentUrl.length;
        this.params.yqUnits = localStorage.getItem("departmentName");
        this.params.suggest = val.suggest ? val.suggest : "";
        this.promptNum = val.promptNum ? val.promptNum : "";

        if (this.title == "提示单转办") {
          this.sendOrgId = "";
          this.yqReceiver = "";
          this.sendPhone = "";
          this.promptUserId = Number(localStorage.getItem("userId"));
          this.promptUserPhone = localStorage.getItem("iphone");
        } else {
          this.sendOrgId = val.sendOrgId ? val.sendOrgId : "";
          this.yqReceiver = val.yqReceiver ? val.yqReceiver : "";
          this.sendPhone = val.sendPhone ? val.sendPhone : "";
          this.promptUserId = val.promptUserId
            ? val.promptUserId
            : Number(localStorage.getItem("userId"));
          this.promptUserPhone = val.promptUserPhone
            ? val.promptUserPhone
            : localStorage.getItem("iphone");
        }
        this.receiverList = val.receiverList;
        if (!this.promptNum) {
          this.getYqNubmer();
        }
      },
      deep: true,
      immediate: true,
    },
    pathList: {
      handler(val) {
        this.pathLists = val;
      },
      deep: true,
      immediate: true,
    },
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {
    if (this.title != "提示单转办") {
      this.getYqNubmer();
    }
    this.getSectionList(1);
    console.log(this.data);
  },
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.inBlock {
  display: inline-block;
}
.dx {
  width: 300px;
  height: 440px;
  overflow-y: auto;
  border: 1px solid #d0d0d0;
  background: #f2f2f2;
  border-radius: 4px;
  margin-top: 10px;
  padding: 20px 30px 0px 20px;
  font-size: 16px;
  color: #929292;
  .xs {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 4; /* 控制显示的行数 */
  }
}
.ckBox {
  // display: inline-block;
  width: 16px;
  height: 16px;
  background: #fff;
  border: 2px solid #5585ec;
  cursor: pointer;
  border-radius: 2px;
}
.content-title {
  width: 80px;
  color: #5585ec;
  font-size: 16px;
  font-family: PingFang SC;
  margin-top: 7px;
  text-align: right;
}
.content {
  display: flex;
  margin: 10px 0px 20px 0px;

  .old-text {
    /deep/ .ivu-input {
      height: 200px;
      border: 1px solid #3b5576;
      font-family: PingFang SC;
      color: #999999;
      font-size: 14px;
    }
  }

  .old-textNew {
    width: 609px;
    height: 200px;
    border: 1px solid #3b5576;
    font-size: 14px;
    font-family: PingFang SC;
    color: #999999;
    overflow: hidden;
    .old-textNews {
      height: 140px;
      margin-bottom: 10px;
      /deep/ .ivu-input {
        height: 140px;
        border: 0px solid #3b5576;
        font-family: PingFang SC;
        color: #999999;
        font-size: 16px;
      }
    }
    .news {
      /deep/ .ivu-input {
        height: 40px;
        border: 0px solid #3b5576;
        font-family: PingFang SC;
        color: #999999;
        font-size: 16px;
      }
    }
  }

  .abstract-text {
    /deep/ .ivu-input {
      height: 100px;
      border: 1px solid #3b5576;
    }
  }

  .content-number {
    /deep/ .ivu-input {
      height: 38px;
      border: 1px solid #3b5576;
      font-family: PingFang SC;
      color: #999999;
      font-size: 14px;
    }

    /deep/ .ivu-select-single .ivu-select-selection {
      height: 38px;
      border: 1px solid #3b5576;
      font-family: PingFang SC;
      color: #999999;
      font-size: 14px;
    }
  }
}
.xian {
  position: absolute;
  top: 0px;
  right: 320px;
  height: 100%;
  border-right: 1px solid #ededed;
}
.foot {
  text-align: center;
  margin: 20px 0px;
  .foots {
    display: inline-block;
    width: 80px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #5585ec;
    border-radius: 4px;
    font-family: PingFang SC;
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
  }
}
/deep/.unitFrame > .ivu-select > .ivu-select-dropdown {
  display: none;
}
</style>
