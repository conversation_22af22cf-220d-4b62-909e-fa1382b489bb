<!-- 入口页 -->
<template>
  <div style="width:730px;padding:16px;">
   <p style="
          color: #5585ec;
          font-size: 20px;
          margin: 26px 0px 26px 0px;
          text-align: center;
        ">该提示单确定已完结？</p>
    <div class="foot">
      <span class="foots" @click="submit()">确定</span>
      <span
        class="foots"
        style="margin-left: 70px; background: #999999"
        @click="close()"
        >关闭</span
      >
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  //生命周期 - 创建完成（访问当前this实例）
  props: {
    datas: {
      default: () => {},
    },
    that:{}
  },
  watch: {},
  created() {},
  //方法所在
  methods: {
    submit(){
      this.$emit('handleOver',this.datas)
      this.close()
    },
    close(){
      this.$emit('close')
    },
  
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {},
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */

  .foot {
    text-align: center;
    margin: 20px 0px;
    .foots {
      display: inline-block;
      width: 80px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background: #5585ec;
      border-radius: 4px;
      font-family: PingFang SC;
      font-weight: 600;
      color: #ffffff;
      font-size: 14px;
      cursor: pointer;
    }
  }
</style>
