<template>
  <div class="SentUnit" ref="SentUnit">
    <div class="shelter"></div>
    <div class="search">
      <span class="label">
        单位搜索：
      </span>
      <Select
        v-model="sendOrgId"
        style="width: 260px;"
        filterable
        @on-change="onChange"
      >
        <Option
          v-for="item in officeList"
          :value="item.organId"
          :key="item.organId"
          >{{ item.organName }}
        </Option>
        <Option
          v-for="item in unitList"
          :value="item.organId"
          :key="item.organId"
          >{{ item.organName }}
        </Option>
      </Select>
    </div>
    <div class="ListFrame">
      <div class="officeList">
        <div
          class="item"
          v-for="item in officeList"
          @click="onChange(item.organId)"
          :key="item.organId"
        >
          <span class="item-title">{{ item.organName }}</span>
        </div>
      </div>
      <div
        class="item"
        v-for="item in unitList"
        @click="onChange(item.organId)"
        :key="item.organId"
      >
        <span class="item-title">{{ item.organName }}</span>
      </div>
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
  data() {
    // 这里存放数据
    return {
      officeList: [],
      unitList: [],
      sendOrgId: null,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    allList: {
      default: [],
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    console.log(this.allList);
    this.allList.forEach((i) => {
      if (i.organType === 2) {
        this.officeList.push(i);
      } else {
        this.unitList.push(i);
      }
    });
    console.log(this.officeList);
  },
  // 方法集合
  methods: {
    handleOutsideClick(event) {
      const element = this.$refs.SentUnit;
      // 判断点击是否在元素内部
      if (!element.contains(event.target)) {
        console.log("点击了外边");
        document.removeEventListener("click", this.handleOutsideClick); // 清除监听器
        this.$emit("outside-click"); // 触发外部点击事件
      } else {
        console.log("点击了内部");
      }
    },
    onChange(d) {
      console.log(d);
      this.$emit("confirm", d);
      this.$emit("outside-click"); // 触发外部点击事件
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    console.log("添加监听事件");
    setTimeout(() => {
      document.addEventListener("click", this.handleOutsideClick);
    });
  },
  beforeDestroy() {
    document.removeEventListener("click", this.handleOutsideClick);
  },
};
</script>
<style scoped lang="less">
.SentUnit {
  position: absolute;
  border: 2px solid #e9e9e9;
  border-radius: 10px;
  padding: 10px;
  z-index: 10;
  background-color: #fff;
  .label {
    font-size: 16px;
  }
  .shelter {
    position: absolute;
    top: -40px;
    height: 40px;
    width: 250px;
  }
  .ListFrame {
    width: 900px;
    max-height: 400px;
    overflow-y: auto;
    .item {
      display: inline-block;
      margin: 10px;
      cursor: pointer;
      font-size: 16px;
    }
    .officeList {
      border-bottom: 1px solid #e9e9e9;
    }
  }
}
</style>
