<!-- 入口页 -->
<template>
  <!-- 关联事件 -->
  <div class="events">
    <!-- 事件顶部 -->
    <div class="events-header">
      <div style="display: flex;">
        <div class="events-time">
          <span>时间：</span>
          <DatePicker
            v-model="dateArr"
            placeholder="年/月/日"
            type="daterange"
            split-panels
            style="width: 200px;"
            @on-change="search"
          ></DatePicker>
        </div>
        <div class="events-time">
          <span>状态：</span>
          <Select v-model="status" style="width: 135px;" @on-change="search">
            <Option v-for="(v, k) in statusList" :value="k" :key="k">{{
              v
            }}</Option>
          </Select>
        </div>
      </div>

      <div class="events-inputs">
        <Input
          v-model="keyword"
          placeholder="请输入关键词"
          style="width: 200px;"
          search
          @on-search="search"
        />
      </div>
    </div>

    <div class="events-xian"></div>
    <div class="events-list">
      <div class="events-nav">
        <span
          v-for="itm in navList"
          :key="itm.id"
          class="inBlock navs"
          :style="{ width: itm.width }"
          >{{ itm.title }}</span
        >
      </div>

      <div class="listBox">
        <Spin v-show="loading" fix>
          <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <no-data v-show="total === 0 && !loading" />
        <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
          <div
            class="events-lists flex"
            v-for="(item, index) in eventList"
            :key="item.eventId"
            :style="
              (index + 1) % 2 == 0
                ? 'background:rgba(245, 245, 245, 0.47);'
                : ''
            "
          >
            <span class="inBlock" style="width: 97.5px;">
              <Checkbox :label="item.eventId">{{ index + 1 }}</Checkbox>
            </span>
            <span class="inBlock ellipsis" style="flex: 1;">{{
              item.eventName
            }}</span>
            <span class="inBlock" style="width: 200px;">{{
              moment(item.createTime).format("YYYY-MM-DD hh:mm:ss")
            }}</span>
          </div>
        </CheckboxGroup>
      </div>
      <Page
        v-show="total > 0 && !loading"
        :total="total"
        show-elevator
        show-total
        :current="pageNo"
        :page-size="pageSize"
        @on-change="pageNoChange"
      />
    </div>

    <div class="foot">
      <span class="foots" @click="submit()">确定</span>
      <span
        class="foots"
        style="margin-left: 70px; background: #999999;"
        @click="close()"
        >关闭</span
      >
    </div>
  </div>
</template>

<script>
import moment from "moment";
export default {
  data() {
    return {
      moment,
      checkAllGroup: [],
      eventList: [],
      navList: [
        { id: 1, title: "序号", width: "97.5px" },
        { id: 2, title: "标题", width: "382px" },
        { id: 3, title: "创建时间", width: "186.5px" },
      ], //关联事件标题
      statusList: {
        0: "全部",
        1: "监测中",
        2: "已暂停",
      },

      dateArr: [],
      keyword: "",
      status: "0",
      loading: false,
      total: 0,
      pageNo: 1,
      pageSize: 20,
    };
  },
  props: {
    data: {
      default: () => {},
    },
    that: {},
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {},
  //方法所在
  methods: {
    submit() {
      if (this.that.$route.path === "/main/monitor/jiNanHotList") {
        this.$emit("handleRelevance", this.checkAllGroup);
        this.close();
      } else {
        this.$emit("handleInclidents", this.checkAllGroup);
        this.close();
      }
    },
    close() {
      this.$emit("close");
    },
    search() {
      this.pageNo = 1;
      this.getEventList();
    },
    pageNoChange(d) {
      this.pageNo = d;
      this.getEventList();
    },
    getEventIdsByPromptId() {
      let params = {
        promptId: this.data.promptMsgId,
      };
      this.that.$http
        .get("/prompt/getEventIdsByPromptId", { params })
        .then((res) => {
          console.log(res);
          this.checkAllGroup = res.body.data;
        });
    },
    getEventList() {
      this.loading = true;
      this.eventList = [];
      let params = {
        starDate: this.dateArr[0] ? this.dateArr[0] : null,
        endDate: this.dateArr[1] ? this.dateArr[1] : null,
        keyword: this.keyword,
        status: this.status,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
      };
      this.that.$http
        .get("/prompt/getEventByPromptId", { params })
        .then((res) => {
          console.log(res);
          this.total = res.body.data.count;
          if (this.total > 0) {
            this.eventList = res.body.data.data;
          }
          this.loading = false;
        });
    },
    checkAllGroupChange(d) {
      console.log(d);
    },
    getEventIdsByHotRank() {
      let params = {
        hotRankId: this.data.id,
      };
      console.log(this.that.$route);
      this.$http.get("/hotrank/getHotRankRelation", { params }).then((res) => {
        console.log(res.body.data);
        this.checkAllGroup = res.body.data.map((i) => i.eventId);
      });
    },
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {
    if (this.that.$route.path === "/main/monitor/jiNanHotList") {
      this.getEventIdsByHotRank();
    } else {
      this.getEventIdsByPromptId();
    }
    this.getEventList();
  },
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.events {
  width: 730px;
  padding: 16px;
  .listBox {
    position: relative;
    height: 400px;
    overflow-y: auto;
  }
  .events-header {
    display: flex;
    justify-content: space-between;
    padding: 9px 16px 24px 6px;
    .events-time {
      font-family: PingFang SC;
      // color: #333333;
      font-size: 16px;
      margin-right: 10px;
    }
  }
  .events-xian {
    border-bottom: 0.5px dashed #707070;
    margin: 0px 3.5px 0px 6.5px;
  }
  .events-list {
    .events-nav {
      padding-left: 30px;
      margin-bottom: 10.07px;
      .navs {
        text-align: left;
        font-family: PingFang SC;
        font-weight: 600;
        color: #333333;
        font-size: 16px;
      }
    }
    .events-lists {
      padding-left: 30px;
      height: 64px;
      line-height: 64px;
      background: rgba(235, 237, 248, 0.47);
      span {
        font-size: 16px;
        font-family: PingFang SC;
      }
      /deep/.ivu-checkbox-wrapper {
        font-size: 16px;
      }
    }
  }
}
.inBlock {
  display: inline-block;
}
.foot {
  text-align: center;
  margin: 20px 0px;
  .foots {
    display: inline-block;
    width: 80px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #5585ec;
    border-radius: 4px;
    font-family: PingFang SC;
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
