<template>
  <div class="homePage">
    <div class="header">
      <svg-icon icon-class="智能助手"></svg-icon>
      <div class="title">
        <div class="text">智能助手</div>
        <div class="remark">网信大模型驱动</div>
      </div>
      <PackUp />
    </div>
    <div class="body">
      <div class="rowBox">
        <Send @send="send" />
      </div>
      <div class="classifyName">猜你喜欢</div>
      <div
        class="rowBox cp"
        :style="{ marginTop: index !== 0 ? '10px' : '0px' }"
        v-for="(item, index) in guess"
        :key="item.id"
        @click="recommend(item)"
      >
        {{ item.keyword }}
      </div>

      <div class="classifyName">最近的会话</div>
      <template v-for="(item, index) in historyList">
        <div :key="index">
          <div class="date">
            {{ item.day }}
          </div>
          <div class="rowBox conversation">
            <div
              class="item flex cp"
              v-for="i in item.list"
              :key="i.conversationId"
              @click="send(i, '1')"
            >
              <div class="iconBox cp">
                <Icon type="md-text" />
              </div>
              <div class="content flex">
                <div class="info flex sb">
                  <span>
                    {{ i.title }}
                  </span>
                  <svg-icon
                    icon-class="榜单-删除"
                    class="cp"
                    @click.native.stop="del(i.conversationId)"
                  ></svg-icon>
                </div>
                <div class="remark flex sb">
                  <span>
                    {{ i.instruction }}
                  </span>
                  <span>
                    {{ i.time }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import PackUp from "../components/packUp.vue";
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import OrderTag from "../components/orderTag.vue";
import Send from "../components/send.vue";

export default {
  data() {
    // 这里存放数据
    return {
      guess: [],
      historyList: [],
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { PackUp, OrderTag, Send },
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    del(id) {
      let params = { conversationIds: id };
      this.$http
        .post("/model/conversationDelete", params, { emulateJSON: true })
        .then((res) => {
          this.getHistory();
          if (res.body.status === 0) {
            this.$Message.success("删除成功");
          }
        });
    },
    // 猜你喜欢
    recommend(d) {
      console.log(d);
      let params = { instruction: "", question: d.keyword };
      this.send(params, '2');
    },
    //发送信息  切换到对话窗口
    send(data, type) {
      if(type == '1'){
        this.getLog('智能助手/智能助手', '最近会话/' + data.question)
      }else if(type == '2'){
        this.getLog('智能助手/智能助手', '猜你喜欢/'  + data.question)
      }else{
        this.getLog('智能助手/智能助手', '智能分析//' + data.instruction + ' ' + data.question)
      }
      console.log(data);
      this.$emit("send", data);
    },
    // 获取配置-猜你喜欢列表
    getConfig(type) {
      let params = {
        type: type,
      };
      this.$http.get("/model/getConfig", { params }).then((res) => {
        console.log(res);
        this.guess = res.body.data;
      });
    },
    // 获取历史会话
    getHistory() {
      this.$http
        .get("/model/aiModelLogHistory")
        .then((res) => {
          console.log(res.data);
          this.historyList = res.body.data;
          console.log(this.historyList);
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getConfig(1);
    this.getHistory();
  },
};
</script>
<style scoped lang="less">
.homePage {
  .header {
    height: 190px;
    background: linear-gradient(90deg, #ffffff 0%, #e6eeff 100%);
    border-radius: 8px 0px 0px 8px;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.16);
    display: flex;
    align-items: center;
    padding: 0 0 0 30px;

    .svg-icon {
      width: 97px;
      height: 152px;
    }

    .title {
      padding-left: 30px;
      height: 90px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;

      .text {
        font-weight: 600;
        color: #5585ec;
        font-size: 30px;
      }

      .remark {
        font-weight: 600;
        color: #999999;
        font-size: 20px;
      }
    }
  }

  .body {
    padding: 30px 25px;
    height: calc(~"100vh - 190px");
    overflow-y: auto;

    .date {
      color: #666666;
      font-size: 16px;
      margin-bottom: 10px;
    }

    .rowBox {
      position: relative;
      min-height: 56px;
      background: #ffffff;
      border-radius: 4px;
      box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.16);
      padding: 0 12px;
      display: flex;
      align-items: center;
      color: #333333;
      font-size: 16px;

      .content {
        padding: 0 20px;
        flex: 1;
        align-items: center;

        .info,
        .remark {
          width: 100%;
        }
      }

      .iconBox {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: #c9d9f9;

        .ivu-icon {
          font-size: 22px;
        }
      }

      .remark {
        color: #666666;
        font-size: 14px;
      }
    }

    .conversation {
      flex-direction: column;

      .item {
        width: 100%;
        height: 70px;
        align-items: center;
        border-top: 1px solid #c7c7c7;

        &:nth-child(1) {
          border: none;
        }

        .content {
          height: 100%;
          flex-direction: column;
          justify-content: space-between;
          padding: 10px 20px;
        }

        .info {
          align-items: center;
        }
      }
    }
  }

  .classifyName {
    padding: 20px 0;
    color: #5585ec;
    font-size: 20px;
  }
}
</style>
