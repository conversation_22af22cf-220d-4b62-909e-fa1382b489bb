<template>
  <div class="chat" :style="{ right: open ? '0px' : '-730px' }">
    <component
      :is="componentId"
      @send="send"
      @backspace="backspace"
      :sessionInfo="sessionInfo"
    ></component>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import HomePage from "./home/<USER>";
import Conversation from "./conversation";
export default {
  data() {
    // 这里存放数据
    return {
      componentId: "HomePage",
      sessionInfo: {},
      open: false,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { HomePage, Conversation },
  props: {
    show: {
      default: false,
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    backspace() {
      this.componentId = "HomePage";
    },
    send(data) {
      this.componentId = "Conversation";
      this.sessionInfo = data;
    },
    close() {
      this.$emit("close");
    },
  },
  // 计算属性 类似于 data ���念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    show: {
      handler(val) {
        this.open = val;
      },
    },
  },
  //过滤器
  filters: {},
  // 生命周期 - ��载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.chat {
  width: 730px;
  height: 100vh;
  background: linear-gradient(180deg, #ffffff 0%, #e6efff 100%);
  border-radius: 8px 0 0 8px;
  box-shadow: 2px 0 0 rgba(0, 0, 0, 0.16);
  position: fixed;
  transition: right 0.5s ease-in-out;
  z-index: 1000;
}
</style>
