<template>
  <div class="packUp" @click="packUp">
    <span>
      点击收起
    </span>
    <Icon type="md-arrow-dropright" />
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    packUp() {
      const upClose = (dom) => {
        console.log(dom);
        if (typeof dom.close === "function") {
          dom.close();
        } else {
          upClose(dom.$parent);
        }
      };
      upClose(this.$parent);
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.packUp {
  width: 132px;
  height: 50px;
  background: #ffffff;
  border-radius: 100px 0px 0px 100px;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  line-height: 50px;
  color: #5585ec;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
