<template>
  <div class="send flex sb">
    <Icon
      v-if="!instruction"
      type="ios-add-circle"
      class="add-circle"
      @click.native="orderShow = !orderShow"
    />
    <OrderTag
      v-else
      :text="instruction"
      class="cp"
      @click.native="orderShow = !orderShow"
    />
    <div class="text" v-if="!inputStatus" @click.stop="openInput">
      请向我提问或输入“/”呼出指令
    </div>
    <Input
      v-else
      :class="[!instruction ? 'textInput-30' : 'textInput-120']"
      v-model="text"
      type="textarea"
      @on-change="valChanged"
      @on-keydown="handleKeydown"
      :maxlength="500"
      @on-enter="send"
      ref="inputRef"
    />
    <div class="paper-plane">
      <span v-show="inputStatus">{{ text.length }}/500</span>
      <Icon type="ios-paper-plane" @click.native="send" />
    </div>
    <TypePrompt
      :style="{
        top:
          direction === 'bottom' ? (inputStatus ? '100px' : '60px') : '-480px',
      }"
      v-show="orderShow"
      @change="orderChange"
    />
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import TypePrompt from "./typePrompt.vue";
import orderList from "../json/order.json";
import OrderTag from "./orderTag.vue";

export default {
  data() {
    // 这里存放数据
    return {
      text: "",
      orderList,
      orderShow: false,
      instruction: "",

      inputStatus: false,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { TypePrompt, OrderTag },
  props: {
    direction: {
      default: "bottom",
    },
    historyText: {
      default: "",
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    openInput() {
      this.inputStatus = true;
      this.$nextTick(() => {
        this.$refs.inputRef.focus();
      });
    },
    send() {
      if (this.text.length == 0 && this.historyText.length == 0) {
        return false;
      }
      let params = {
        instruction: this.instruction,
        question: this.text ? this.text : this.historyText,
      };
      let status = this.$emit("send", params);
      if (status !== false) {
        this.text = "";
        this.instruction = "";
      }
    },
    handleKeydown(event) {
      // 检查是否按下了 Backspace 或 Delete 键
      if (event.key === "Backspace" || event.key === "Delete") {
        // 检查输入框是否为空
        if (this.text === "") {
          this.instruction = "";
        }
      }
    },
    orderChange(data) {
      if (this.text === "/") {
        this.text = "";
      }
      this.instruction = data.name;
      this.inputStatus = true;
      this.orderShow = false;
      this.$nextTick(() => {
        this.$refs.inputRef.focus();
      });
    },
    valChanged(d) {
      this.orderShow = d.data === "/";
    },
    handleOutsideClick(event) {
      if (
        !this.$el.contains(event.target) &&
        this.instruction === "" &&
        this.text === ""
      ) {
        this.inputStatus = false;
        this.removeClickOutsideListener();
      }
    },
    // 添加监听事件
    addClickOutsideListener() {
      document.addEventListener("click", this.handleOutsideClick);
    },
    // 移除监听事件
    removeClickOutsideListener() {
      document.removeEventListener("click", this.handleOutsideClick);
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    inputStatus(d) {
      if (d) {
        this.addClickOutsideListener();
      }
    },
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.send {
  width: 100%;
  min-height: 56px;
  background-color: #fff;

  position: relative;

  /deep/ .orderTag {
    position: absolute;
    top: 6px;
    z-index: 10;
  }

  .textInput-30 {
    /deep/ .ivu-input {
      text-indent: 30px;
    }
  }

  .textInput-120 {
    /deep/ .ivu-input {
      text-indent: 120px;
    }
  }

  .text {
    padding-left: 40px;
    flex: 1;
    line-height: 50px;
  }

  .add-circle {
    background-color: #fff;
    position: absolute;
    top: 10px;
  }

  .paper-plane {
    position: absolute;
    bottom: 10px;
    right: 0;
    background-color: #fff;

    span {
      font-size: 12px;
      color: #999;
    }
  }

  /deep/ .ivu-input {
    border: none;
    color: #333333;
    font-size: 16px;
    line-height: 44px;
    min-height: 44px;

    &:focus {
      box-shadow: 0px 0px 5px 0px rgba(64, 159, 255, 0);
      border: none;
      outline: none;
    }
  }
}

.ivu-icon {
  font-size: 28px;
  color: #5585ec;
  cursor: pointer;
  z-index: 10;
}
</style>
