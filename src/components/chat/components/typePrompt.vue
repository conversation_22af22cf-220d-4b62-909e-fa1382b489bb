<template>
  <div class="typePrompt flex">
    <div class="classify">
      <div
        :class="['item', 'cp', 'flex', classId === item.id ? 'active' : '']"
        v-for="item in list"
        :key="item.id"
      >
        <svg-icon
          :icon-class="classId === item.id ? item.svgIcon + '-s' : item.svgIcon"
        />
        <div>{{ item.name }}</div>
      </div>
    </div>
    <div class="order">
      <div
        class="assemble"
        v-for="item in list"
        :key="item.svgIcon"
        v-show="Object.keys(item.childList).length"
      >
        <div class="title">
          {{ item.name }}
        </div>
        <div class="childBox">
          <div
            class="item flex cp"
            v-for="(v, k) in item.childList"
            :key="k"
            @click="selected(v)"
            v-if="k"
          >
            <svg-icon icon-class="diannao" />
            <div class="content">
              <div class="title">/{{ k }}</div>
              <div class="remark">{{ v.remark }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import chatStatus from "@/assets/json/chatStatus.json";
const list = [
  { name: "常用", svgIcon: "shizhong", id: "1", childList: [] },
  {
    name: "生成",
    svgIcon: "shengcheng",
    id: "3",
    childList: chatStatus,
  },
];
export default {
  data() {
    // 这里存放数据
    return { list, classId: "1" };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    selected(data) {
      this.$emit("change", data);
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.typePrompt {
  position: absolute;
  width: 100%;
  background-color: #fff;
  height: 480px;
  left: 0;
  z-index: 2;
  .classify {
    width: 110px;
    background-color: #ebedf8;
    padding: 20px 15px;
    height: 100%;
    .item {
      width: 80px;
      height: 80px;
      background: #ffffff;
      border-radius: 4px;
      color: #333333;
      font-size: 16px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;

      .svg-icon {
        width: 30px;
        height: 30px;
      }
    }
    .active {
      color: #5585ec;
    }
  }
  .order {
    padding: 12px;
    overflow-y: auto;
    width: 100%;
    .assemble {
      color: #333333;
      font-size: 16px;

      & > .title {
        margin-bottom: 14px;
      }
      .childBox {
        .item {
          margin-bottom: 14px;
          align-items: center;
          height: 42px;
          .svg-icon {
            width: 38px;
            height: 38px;
            margin-right: 10px;
          }
          .remark {
            color: #666666;
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
