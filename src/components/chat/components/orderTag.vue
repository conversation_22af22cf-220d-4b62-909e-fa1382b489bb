<template>
  <div class="orderTag flex">
    <svg-icon icon-class="diannao" />
    <div class="title">/{{ text }}</div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    text: {
      default: "",
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {},
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.orderTag {
  position: absolute;
  height: 44px;
  background: #f7f7f7;
  border-radius: 4px;
  color: #333333;
  font-size: 16px;
  align-items: center;
  padding: 0 8px;
  .svg-icon {
    width: 20px;
    height: 18px;
    margin-right: 10px;
  }
}
</style>
