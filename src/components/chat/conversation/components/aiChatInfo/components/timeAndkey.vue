<template>
  <div class="timeAndkey flex">
    <Icon type="md-alarm" />
    时间：
    <div class="time">
      {{ time }}
    </div>
    关键词：
    <div class="key ellipsis" :title="keyword">
      {{ keyword }}
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    time: {},
    keyword: {},
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {},
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.timeAndkey {
  background: #ffffff;
  border: 1px solid #707070;
  border-radius: 4px;
  width: 540px;
  height: 56px;
  font-size: 16px;
  align-items: center;
  padding: 10px;
  margin-bottom: 20px;

  /deep/ .ivu-icon {
    color: #5585ec;
    margin-right: 10px;
    font-size: 22px;
  }

  & > div {
    flex: 1;
    color: #5585ec;
  }
}
</style>
