<template>
  <div class="messageList">
    <div class="header">
      已为您找到相关信息，如下：
    </div>
    <div class="listBox">
      <div class="item flex" v-for="(item, index) in msgs" :key="index">
        <div class="num">
          {{ index + 1 }}
        </div>
        <div class="text ellipsis">
          {{ item.mtitle?item.mtitle:item.mcontent }}
        </div>
      </div>
    </div>
    <div class="remark">
      我将继续努力收集更全面的信息和新闻资讯，如果您需要关注或避开某些信息类别,也请让我知晓，我将调整为您推送相应信息。
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    msgs: {},
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {},
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    msgs: {
      handler(newVal, oldVal) {
        if (typeof newVal === "string") {
          console.log(JSON.parse(newVal));
        }
      },
      deep: true,
      immediate: true,
    },
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    console.log(this.msgs);
  },
};
</script>
<style scoped lang="less">
.messageList {
  background-color: #fff;
  color: #333333;
  font-size: 16px;
  padding: 20px;
  & > .text {
    margin-top: 20px;
  }
  .header {
    margin-top: 20px;
  }
  .listBox {
    margin-top: 20px;
    border-bottom: 2px solid #999;
    .item {
      margin-bottom: 20px;
      .num {
        width: 40px;
      }
      .text {
      }
      &:nth-child(1) {
        .num {
          color: #f65177;
        }
      }
      &:nth-child(2) {
        .num {
          color: #ff7600;
        }
      }
      &:nth-child(3) {
        .num {
          color: #ffbc00;
        }
      }
    }
  }
  .remark {
    color: #999999;
    margin-top: 20px;
  }
}
</style>
