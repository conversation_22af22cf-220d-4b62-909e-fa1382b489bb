<template>
  <div class="timeline">
    <Timeline>
      <TimelineItem v-for="(item, index) in msgs" :key="index">
        <div class="dian" v-if="index === 0" slot="dot"></div>
        <div class="item">
          <div class="time">{{ item.mpublishTime }}</div>
          <div class="content">
            <span>{{ item.mwebsiteName }}： </span>
            <span style="color: #5585ec"> {{ item.mtitle }}</span>
          </div>
        </div>
      </TimelineItem>
    </Timeline>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  name: "timeline",
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    msgs: {
      default: "",
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    handleTime(d) {},
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {
    time(d) {
      if (d) {
        return d;
      } else {
        return "数据出错";
      }
    },
  },

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style scoped lang="less">
.timeline {
  .item {
    .time {
      color: #999999;
    }

    .content {
      color: #333;
      margin-top: 10px;
    }
  }

  .dian {
    width: 14px;
    height: 14px;
    background: #ffffff;
    border: 3px solid #5585ec;
    border-radius: 50%;
    margin: 0 auto;
  }

  /deep/ .ivu-timeline-item-head {
    border-width: 3px;
    border-color: #c9d9f9;
  }
}
</style>
