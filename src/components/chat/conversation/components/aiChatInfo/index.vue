<template>
  <div class="aiChatInfo flex">
    <div class="avatar">
      <svg-icon icon-class="avatar-ai" />
    </div>
    <div class="content">
      <div class="title flex sb">
        <span class="name">智能助手</span>
        <span class="time">{{ date }}</span>
      </div>
      <div v-if="generate !== false" class="status during flex">
        {{
          msgs.instruction
            ? chatStatus[msgs.instruction].courseText
            : "正在思考中"
        }}
        <Loading />
      </div>
      <div class="status flex" v-else>
        <Icon type="ios-checkmark-circle-outline" />
        {{
          msgs.instruction ? chatStatus[msgs.instruction].resultText : "已完成"
        }}
      </div>
      <div class="details" v-if="displayedText || msgs.answer">
        <TimeAndKey
          v-if="
            msgs.time &&
            msgs.instruction &&
            chatStatus[msgs.instruction].timeAndKey
          "
          :keyword="msgs.keyword"
          :time="msgs.time"
        />
        <div class="isExpand btn" @click="expandStatus">
          已深度分析<Icon
            :type="isExpand ? 'ios-arrow-up' : 'ios-arrow-down'"
          />
        </div>
        <!-- <div class="think" v-html="think"></div> -->
        <div class="text" v-html="text"></div>
        <component
          v-if="msgs.answer"
          :is="chatStatus[msgs.instruction].componentId"
          :msgs="isString(msgs.msgs) ? JSON.parse(msgs.msgs) : msgs.msgs"
        />
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";

// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import Loading from "./components/loading.vue";
import chatStatus from "@/assets/json/chatStatus.json";

import MessageList from "./components/messageList.vue";
import TimeLine from "./components/timeline.vue";

import markdownit from "markdown-it";
import TimeAndKey from "./components/timeAndkey.vue";

export default {
  data() {
    // 这里存放数据
    return {
      chatStatus,
      status: "during",

      displayedText: "",
      index: 0,

      timeOut: null,
      think: "", //思考的内容
      text: "", //正文
      isExpand: true,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { Loading, MessageList, TimeAndKey, TimeLine },
  props: {
    generate: {
      default: false,
    },
    msgs: {
      default: () => {
        return {
          instruction: null,
        };
      },
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    expandStatus() {
      this.isExpand = !this.isExpand;
      let dom = document.getElementById(this.msgs.id);
      console.log(dom);

      this.$nextTick(() => {
        dom.style.display = this.isExpand ? "block" : "none";
      });
    },
    type() {
      if (this.index < this.text.length) {
        this.displayedText += this.text.charAt(this.index);
        this.index++;
        let time;
        // 根据文本长度，来获得打字机效果的速度
        if (this.text.length > 50) {
          time = this.getRandomBetween(5, 50);
        } else {
          time = this.getRandomBetween(50, 150);
        }

        this.timeOut = setTimeout(this.type, time); // 设置打字速度，这里是100毫秒
      } else {
        this.$emit("finish");
        this.$emit("printChange");
      }
    },
    // 取范围内的随机值
    getRandomBetween(min, max) {
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },

    isString(value) {
      return Object.prototype.toString.call(value) === "[object String]";
    },
    formattedText(str = this.msgs.answer) {
      console.log(str);
      //如果是在回答中的状态
      let text = "";
      if (this.generate) {
        // text = this.displayedText; //则使用打字机效果渲染
        text = str;
      } else {
        //如果不是在回答中的状态，那就是在在现实历史记录的时候
        // 先进行内容判断，是否是"抱歉，未找到您需要的数据，请换个搜索条件试试"，如果是，那就直接返回就不需要后续处理
        if (this.msgs.answer.indexOf("抱歉，未找到您需要的数据") !== -1) {
          text = this.msgs.answer;
        } else {
          // 在这里如果有类型，根据类型拿到前缀和回答内容拼接，没有类型直接使用回答
          text = this.msgs.instruction
            ? this.msgs.instruction
              ? this.chatStatus[this.msgs.instruction].prefix + this.msgs.answer
              : ""
            : this.msgs.answer;
        }
      }
      if (!this.generate && !text) {
        text = "暂无数据";
      }
      text = text.replaceAll("\n---\n", "\n<hr>\n");
      text = text.replaceAll("\n--- \n", "\n<hr> \n");
      text = text.replaceAll("\n\n", "<br /><br />  \n\n");
      let md = markdownit({
        // html: true,
        // breaks: true,
        // tables: true, // 开启 GFM 表格语法

        html: true, // 允许 HTML 标签
        breaks: true, // 关闭单换行符转 <br>
        tables: true, // 启用 GFM 表格
        typographer: true, // 可选：增强排版（如引号转换）
      });
      text = md.render(text);
      text = text.replaceAll(
        "<think>",
        "<header id='" + this.msgs.id + "' style='display:black'>"
      );
      text = text.replaceAll("</think>", "</header>");
      this.text = text.replaceAll("<br><br>", "<br>");
      console.log(this.text);
      this.$forceUpdate(); // 强制 Vue 更新
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    date() {
      let createTime = this.msgs ? this.msgs.createTime : new Date();
      let createDay = this.msgs ? this.msgs.createDay : new Date();
      let time = moment(createTime).format("HH:mm");
      let day = moment(createDay).isSame(moment(), "day")
        ? "今日"
        : moment(createDay).format("MM-DD");
      return `${day} ${time}`;
    },
  },
  // 监控 data 中的数据变化
  watch: {
    "msgs.answer": {
      handler(newVal, oldVal) {
        console.log(newVal);
        let prefix =
          this.msgs.instruction &&
          newVal.indexOf("抱歉，未找到您需要的数据") === -1
            ? this.chatStatus[this.msgs.instruction].prefix
            : "";
        // this.text = prefix + newVal;
        this.formattedText(prefix + newVal);
        // if (this.generate && this.index === 0) {
        //   this.type();
        // }
      },
      deep: true,
    },
  },
  //过滤器
  filters: {},
  beforeDestroy() {
    clearTimeout(this.timeOut);
  },
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.formattedText();
  },
};
</script>
<style scoped lang="less">
.aiChatInfo {
  .avatar {
    .svg-icon {
      margin-top: 20px;
      width: 56px;
      height: 56px;
    }
  }

  .content {
    margin-left: 10px;
    max-width: 580px;

    .title {
      width: 220px;
      color: #999999;
      font-size: 14px;
    }

    .status {
      width: 220px;
      height: 56px;
      color: #333333;
      font-size: 16px;
      padding: 0 12px;
      align-items: center;
      border-radius: 4px;
      background: #ffffff;
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.16);
      margin-bottom: 20px;

      /deep/ .ivu-icon {
        font-size: 20px;
        color: #00c4d5;
        margin-right: 10px;
      }
    }

    .during {
      background: linear-gradient(90deg, #5585ec 0%, #f65177 100%);
      box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.16);
      color: #ffffff;
    }

    .details {
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;

      .text {
        //padding: 20px 0;
        margin-bottom: 20px;
      }
    }
  }
}
/deep/ ul {
  padding: 0 0 0 20px;
  li {
    list-style: disc;
  }
}
/deep/ header {
  border-left: 2px solid #e7e8f2;
  padding-left: 20px;
}
.isExpand {
  width: 150px;
}
</style>
