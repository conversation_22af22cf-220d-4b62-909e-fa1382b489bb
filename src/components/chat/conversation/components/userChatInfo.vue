<template>
  <div class="userChatInfo flex">
    <div class="content flex">
      <div class="tag" v-if="instruction">/{{ instruction }}</div>
      <span :style="{ marginLeft: instruction ? '90px' : '0px' }">
        {{ text }}</span
      >
    </div>
    <div class="avatar">
      <svg-icon icon-class="avatar-user" />
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  props: {
    instruction: {
      default: null,
    },
    text: {
      default: "",
    }
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
  },
  // 方法集合
  methods: {},
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    text: {
      handler(newVal, oldVal) {
        console.log("new text:" + newVal);
      },
      deep: true,
    }
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    console.log("userCatInfo mounted");
    this.$emit("printChange");
  },
};
</script>
<style scoped lang="less">
.userChatInfo {
  justify-content: right;
  margin-bottom: 20px;
  margin-top: 20px;

  .content {
    padding: 12px 17px;
    background: #5585ec;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.16);
    flex-wrap: wrap;
    border-radius: 4px;
    max-width: 580px;
    margin-right: 10px;
    position: relative;

    .tag {
      display: inline-block;
      width: 80px;
      height: 30px;
      background: #ffffff;
      border-radius: 4px;
      line-height: 30px;
      text-align: center;
      position: absolute;
    }

    span {
      line-height: 30px;
      color: #ffffff;
      font-size: 16px;
      white-space: normal; /* 允许 span 内部正常换行 */
      overflow-wrap: break-word; /* 允许在单词内部进行断行 */
    }
  }

  .avatar {
    .svg-icon {
      width: 56px;
      height: 56px;
    }
  }
}
</style>
