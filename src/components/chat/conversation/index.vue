<template>
  <div class="flex sb conversation">
    <div class="header flex sb">
      <div class="title flex cp">
        <svg-icon icon-class="ai-回退" @click.native="backspace" />
        <span>
          {{ sessionInfo.title }}
        </span>
      </div>
      <PackUp />
    </div>
    <div class="chatBox" ref="chatBox">
      <div v-for="item in historyList" :key="item.id">
        <UserChatInfo :instruction="item.instruction" :text="item.question" />
        <AiChatInfo :msgs="item"></AiChatInfo>
      </div>
      <template v-if="waiting">
        <UserChatInfo
          :instruction="instruction"
          :text="userText"
          @printChange="scrollToBottom"
          :key="itemKey"
        />
        <AiChatInfo
          :generate="waiting"
          :msgs="msgs"
          @finish="getSessionInfo"
          @printChange="scrollToBottom"
        ></AiChatInfo>
      </template>
    </div>
    <Send
      direction="top"
      @send="send"
      :historyText="
        historyList.length > 0
          ? historyList[historyList.length - 1].question
          : ''
      "
    />
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import PackUp from "../components/packUp.vue";
import UserChatInfo from "./components/userChatInfo";
import AiChatInfo from "./components/aiChatInfo/index.vue";
import Send from "../components/send.vue";

export default {
  data() {
    // 这里存放数据
    return {
      sessionId: null,
      historyList: [],
      eventSource: null,
      userText: "", //用户提问的文字
      // aiText: "", //ai助手正在回答
      waiting: false, //ai助手正在回答的状态
      instruction: null, //用户提问的指令
      itemKey: 0,
      msgs: {
        instruction: "",
        answer: "",
      },
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { PackUp, UserChatInfo, AiChatInfo, Send },
  props: {
    sessionInfo: {
      default: () => {},
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  beforeDestroy() {
    this.closeEventSource();
  },
  // 方法集合
  methods: {
    getSessionInfo(id = this.sessionId) {
      let params = {
        conversationId: id,
      };
      this.$http
        .get("/model/conversationDetail", { params })
        .then((res) => {
          //清除问询记录
          this.userText = "";
          this.instruction = null;
          this.waiting = false;
          this.msgs = {
            instruction: "",
            answer: "",
          };
          // this.aiText = "";
          this.keyword = null;
          this.time = null;

          // 用历史信息填充
          this.historyList = res.body.data;
          // this.historyList = [
          //   {
          //     id: 2453,
          //     conversationId: "066a5dbb-3ad1-4318-a063-f5c34bd2b2ab",
          //     instruction: "舆情简报",
          //     keyword: "解放阁+(炸街|飙车|轰鸣街|扰民驾驶)",
          //     createTime: 1729656369879,
          //     userId: 47,
          //     startTime: 1728144000000,
          //     endTime: 1729094399000,
          //     createDay: 1729612800000,
          //     questionType: null,
          //     messageId: "656778fc-fea3-4288-8709-a17e6e3e011e",
          //     question: "解放阁炸街\n",
          //     answer:
          //       "### 事件概述\n\n**事件来龙去脉**：近日，济南解放阁附近发生了一起“鬼火少年”飙车、放烟花“炸街”的事件。这些年轻人骑着摩托车，伴随着烟花，仿佛在举行一场街头狂欢。这一行为不仅扰乱了公共秩序，还存在安全隐患，引起了路人的关注和担忧。\n\n**受关注原因**：该事件因年轻人的危险行为、对公共秩序的扰乱以及潜在的安全隐患而受到广泛关注。此外，警方的介入和对涉事人员的处理也进一步引发了公众的讨论。\n\n### 研判分析\n\n- **事件层级**：该事件属于社会治安类事件，涉及公共安全和秩序维护，具有一定的社会影响力。\n- **性质**：事件性质为非法聚集、扰乱公共秩序，同时存在安全隐患。\n- **烈度**：烈度较高，由于事件发生在具有历史意义的解放阁附近，且涉及烟花等危险物品，引发了公众的广泛关注和讨论。\n- **利害关系分析**：主要涉及涉事年轻人、警方、周边居民以及社会公众。涉事年轻人可能面临法律制裁，警方需维护公共秩序，周边居民的生活受到干扰，社会公众对事件的讨论和关注可能影响社会舆论。\n- **潜在爆点**：事件中涉及的危险行为、警方的处理方式以及对青少年教育问题的讨论，都可能成为舆论关注的焦点。\n- **潜在风险**：若处理不当，可能引发公众对社会治安、青少年教育以及公共安全的担忧，甚至可能影响社会的稳定。\n\n### 处置建议\n\n1. **警方应依法处理**：对涉事人员进行教育和处罚，同时加强巡逻和监控，防止类似事件再次发生。\n2. **加强宣传教育**：通过媒体、学校、社区等渠道，加强对青少年的法治教育和安全教育，引导他们树立正确的价值观和行为规范。\n3. **关注青少年心理健康**：家庭、学校和社会应共同关注青少年的心理健康，提供更多的支持和帮助，引导他们以健康的方式表达自我。\n4. **优化社会环境**：为青少年提供更多的健康、安全的活动场所和机会，减少他们接触危险行为的可能性。\n\n通过上述措施，可以有效降低类似事件的发生，维护社会的和谐与稳定。\n\n## 传播分析\n根据小察获取到的统计数据显示，该事件在全网共计发文101876篇次，其中媒体网站平台发文数量最多为63785篇次；短视频-总全部发文数量为19890篇次；客户端发文数量为12155篇次；微信公众号发文数量为3623篇次；新浪微博发文数量为2143篇次；小红书发文数量为256篇次；论坛平台发文数量最少为24篇次。",
          //     msgs: null,
          //     apiCall:
          //       '{"时间":"2024-10-06 00:00:00;2024-10-16 23:59:59","地点":"解放阁","关键词":"解放阁+(炸街|飙车|轰鸣街|扰民驾驶)","情感":"","数据平台":"","事件名称":"解放阁炸街","问题类型":"舆情简报","站点":""}',
          //     time: "10-06 至 10-16",
          //   },
          // ];
          this.scrollToBottom();
        })
        .catch((err) => {
          console.log(err);
        });
    },
    send(data) {
      // 当正在恢复时，禁止发送下一句
      if (this.waiting) {
        return false;
      }
      let params = {
        conversationId: this.sessionId, //会话id，第一次的会话id传空，接口会返回会话id，之后的会话传返回的会话id
        ...data,
      };
      this.waiting = true;
      setTimeout(() => {
        this.getLog(
          "智能助手/智能助手",
          "智能分析//" + data.instruction + " " + data.question
        );
        this.chat(params);
      });
    },
    chat(params) {
      this.userText = params.question;
      this.$http.post("/model/saveLog", params).then((res) => {
        this.setEventSource(res.body.data.id);
        this.sessionId = res.body.data.conversationId;
        this.userText = params.question;
        this.instruction = params.instruction;
        this.msgs.instruction = params.instruction;
        this.itemKey++;
      });
    },
    setEventSource(id) {
      let that = this;
      this.eventSource = new EventSource("/api/model/sse?id=" + id); // 这里是服务器事件流的 URL
      // this.eventSource = new EventSource(
      //   "http://192.168.0.28:8097/model/sse?id=" + id
      // ); // 这里是服务器事件流的 URL

      this.eventSource.onmessage = function (event) {
        try {
          console.log("收到事件:", event);
          const jsonData = JSON.parse(event.data);
          console.log("解析后的 JSON 数据:", jsonData);

          if (jsonData.msgs) {
            that.msgs.msgs = jsonData.msgs;
          }

          if (jsonData.finish === true) {
            console.log("收到完成信号，关闭 EventSource");
            // 关闭通信通道
            that.closeEventSource();
            // 获取历史信息刷新列表
            that.getSessionInfo();
          } else {
            // 依次拼接通道返回的信息
            console.log("拼接数据:", jsonData.data);
            console.log("拼接前的当前 answer:", that.msgs.answer);
            that.$set(that.msgs, "answer", that.msgs.answer + jsonData.data);
            console.log("更新后的 answer:", that.msgs.answer);
          }
        } catch (error) {
          console.error("解析 JSON 时出错:", error);
          that.closeEventSource();
          that.getSessionInfo();
          // 可以在这里加上更多的错误处理逻辑，例如重试逻辑或是警告用户
        }
        that.scrollToBottom();
      };
    },
    closeEventSource() {
      if (this.eventSource) {
        this.eventSource.close();
        console.log("关闭通道");
      }
    },
    scrollToBottom() {
      // Vue.nextTick 确保 DOM 更新完成后执行滚动
      this.$nextTick(() => {
        const container = this.$refs.chatBox;
        container.scrollTop = container.scrollHeight;
      });
    },
    //回退
    backspace() {
      this.$emit("backspace");
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    if (this.sessionInfo.conversationId) {
      this.sessionId = this.sessionInfo.conversationId;
      this.getSessionInfo(this.sessionInfo.conversationId);
    } else {
      let params = {
        instruction: this.sessionInfo.instruction, //产品类型，传空为闲聊
        question: this.sessionInfo.question, //用户输入的文本
      };
      this.sessionInfo.title = this.sessionInfo.question;
      this.waiting = true;
      this.chat(params);
    }
  },
};
</script>
<style scoped lang="less">
.conversation {
  height: 100%;
  flex-direction: column;

  .header {
    align-items: center;
    height: 80px;
    width: 730px;
    height: 80px;
    background: linear-gradient(90deg, #ffffff 0%, #e6eeff 100%);
    border-radius: 8px 0px 0px 8px;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.16);

    .title {
      align-items: center;

      .svg-icon {
        width: 20px;
        height: 20px;
        margin: 0 15px;
      }

      font-weight: 600;
      color: #5585ec;
      font-size: 30px;
    }
  }

  .chatBox {
    flex: 1;
    overflow-y: auto;
    padding: 24px 10px;
  }
}
</style>
