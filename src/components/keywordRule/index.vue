<template>
  <div class="keywordRule">
    <div class="title">关键词填写规则：</div>
    <div>& : 表示同时包含。例如:山东省&济南市，表示同时包含 山东省和济南市</div>
    <div>| : 表示包含任意一个。例如:山东省|济南市，表示包含 山东省或济南市</div>
    <div>- : 表示排除。例如:济南市-山东省，表示包含济南市 但不包含山东省</div>
    <div>
      () : 表示优先运算。例如:济南&(历下区|天桥区)，表示同时包含 济南和历下区 或
      济南和天桥区
    </div>
    <div>? : 表示连接。例如:iPhone?13，表示"iPhone13"</div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {},
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style scoped lang="less">
.keywordRule {
  //width: 484px;
  width: 620px;
  height: 150px;
  background: #ebedf8;
  border-radius: 4px;
  padding: 10px 10px 20px 14px;
  color: #999999;
  font-size: 14px;
  line-height: 22px;
  .title {
    font-weight: 600;
  }
}
</style>
