"use strict";
const path = require("path");
const utils = require("./utils");
const webpack = require("webpack");
const config = require("../config");
const merge = require("webpack-merge");
const baseWebpackConfig = require("./webpack.base.conf");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
//const ExtractTextPlugin = require('extract-text-webpack-plugin')
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
// const OptimizeCSSPlugin = require('optimize-css-assets-webpack-plugin') // "optimize-css-assets-webpack-plugin": "^3.2.0",
// const UglifyJsPlugin = require('uglifyjs-webpack-plugin') // "uglifyjs-webpack-plugin": "^1.1.1",

const env = config.build.env;
// console.log(config);
// let env;
console.log(env);
if (process.env.npm_lifecycle_event == "build") {
  console.log(
    "\n< 生产模式 >，抽离css会多花费少量时间...\n\n*** 测试时可使用npm run test ***\n"
  );
  // env = config.build.env;
} else {
  console.log(
    "\n< 测试模式 >，不抽离css,\n\n=== 生产环境需使用 npm run build ===\n"
  );
  // env = config.test.env;
}
const webpackConfig = merge(baseWebpackConfig, {
  mode:
    process.env.npm_lifecycle_event == "build" ? "production" : "development",
  module: {
    rules: utils.styleLoaders({
      sourceMap: config.build.productionSourceMap,
      extract: process.env.npm_lifecycle_event == "build",
    }),
  },
  devtool: config.build.productionSourceMap ? config.build.devtool : false,
  output: {
    path: config.build.assetsRoot,
    filename: utils.assetsPath("js/[name].[chunkhash].js"),
    chunkFilename: utils.assetsPath("js/[id].[chunkhash].js"),
  },
  /* optimization: {
               splitChunks: {
                 chunks: 'async',
                 minSize: 30000,
                 minChunks: 1,
                 maxAsyncRequests: 5,
                 maxInitialRequests: 3,
                 automaticNameDelimiter: '~',
                 name: true,
                 cacheGroups: {
                   vendors: {
                     test: /[\\/]node_modules[\\/]/,
                     priority: -10
                   },
                   default: {
                     minChunks: 2,
                     priority: -20,
                     reuseExistingChunk: true
                   }
                 }
               },
               runtimeChunk: { name: 'runtime' }
             },*/
  plugins: [
    // http://vuejs.github.io/vue-loader/en/workflow/production.html
    new webpack.DefinePlugin({
      "process.env": env,
    }),
    // 提取css组合到指定文件
    new MiniCssExtractPlugin({
      filename: utils.assetsPath("css/[name].[contenthash].css"),
      chunkFilename: utils.assetsPath("css/[id].[contenthash].css"),
    }),
    // 提取压缩css Compress extracted CSS. We are using this plugin so that possible
    // 去除重复css代码，可能导致css顺序问题 duplicated CSS from different components can be deduped.
    //new OptimizeCSSPlugin({
    //  cssProcessorOptions: {
    //    safe: true
    //  }
    //}),
    // generate dist index.html with correct asset hash for caching.
    // you can customize output by editing /index.html
    // see https://github.com/ampedandwired/html-webpack-plugin
    new HtmlWebpackPlugin({
      filename: config.build.index,
      template: "index.html",
      inject: true,
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeAttributeQuotes: true,
      },
      chunksSortMode: "dependency",
      // chunks: ['manifest', 'vendor', 'app']
      chunks: ["app"],
    }),
    // 通用的一些工具
    new webpack.DllReferencePlugin({
      context: __dirname,
      manifest: require("./dll/common-manifest.json"),
    }),
    // moment相关的
    new webpack.DllReferencePlugin({
      context: __dirname,
      manifest: require("./dll/moment-manifest.json"),
    }),
    //    // d3相关的
    //    new webpack.DllReferencePlugin({
    //      context: __dirname,
    //      manifest: require('./dll/d3-manifest.json')
    //    }),
    // 引用的node_modules会打包到vendor.js
    /*    new webpack.optimize.CommonsChunkPlugin({
                              name: 'vendor',
                              minChunks: function (module, count) {
                                // any required modules inside node_modules are extracted to vendor
                                return (
                                  module.resource &&
                                  /\.js$/.test(module.resource) &&
                                  module.resource.indexOf(
                                    path.join(__dirname, '../node_modules')
                                  ) === 0
                                )
                              }
                            }),*/
    // 异步加载模块等模块管理的核心打包到manifest.js
    /*    new webpack.optimize.CommonsChunkPlugin({
                              name: 'manifest',
                              chunks: ['vendor']
                            }),*/
    // copy custom static assets
    new CopyWebpackPlugin([
      {
        from: path.resolve(__dirname, "../static"),
        to: config.build.assetsSubDirectory,
        ignore: [".*", "README.md"],
      },
    ]),
  ],
});

if (config.build.productionGzip) {
  const CompressionWebpackPlugin = require("compression-webpack-plugin");

  webpackConfig.plugins.push(
    new CompressionWebpackPlugin({
      asset: "[path].gz[query]",
      algorithm: "gzip",
      test: new RegExp(
        "\\.(" + config.build.productionGzipExtensions.join("|") + ")$"
      ),
      threshold: 10240,
      minRatio: 0.8,
    })
  );
}

if (config.build.bundleAnalyzerReport) {
  const BundleAnalyzerPlugin =
    require("webpack-bundle-analyzer").BundleAnalyzerPlugin;
  webpackConfig.plugins.push(new BundleAnalyzerPlugin());
}

module.exports = webpackConfig;
