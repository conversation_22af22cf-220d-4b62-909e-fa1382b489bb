"use strict";
// webpack 通用配置,dev/build均依赖
const path = require("path");
const utils = require("./utils");
const config = require("../config");
const vueLoaderConfig = require("./vue-loader.conf");
const VueLoaderPlugin = require("vue-loader/lib/plugin");

const pxToViewport = require("postcss-px-to-viewport");
function resolve(dir) {
  return path.join(__dirname, "..", dir);
}

module.exports = {
  // 页面中的入口文件。单页面组件单个入口，多页面组件多个入口
  entry: {
    app: "./src/main.js",
    // tool: "./src/tool.js",
  },
  // 输出文件配置
  output: {
    path: config.build.assetsRoot,
    filename: "[name].js",
    publicPath:
      process.env.NODE_ENV === "production"
        ? config.build.assetsPublicPath
        : config.dev.assetsPublicPath,
  },
  resolve: {
    extensions: [".js", ".vue", ".json"],
    alias: {
      vue$: "vue/dist/vue.esm.js",
      "@": resolve("src"),
      "@np": resolve("node_modules"),
    },
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: "babel-loader",
          options: {
            presets: ["@babel/preset-env"],
          },
        },
      },
      {
        test: /\.mjs$/,
        include: /node_modules/,
        type: "javascript/auto",
      },
      {
        test: /\.vue$/,
        include: [resolve("src"), resolve("node_modules/iview")],
        exclude: [/static/, /iview-theme/],
        use: {
          loader: "vue-loader",
          options: vueLoaderConfig,
        },
      },
      {
        test: /\.js$/,
        loader: "babel-loader?cacheDirectory",
        include: [
          resolve("src"),
          resolve("node_modules/webpack-dev-server/client"),
        ],
        exclude: [/static/, /iview-theme/],
        options: {
          cacheDirectory: ".webpack_cache",
        },
      },
      {
        test: /\.js$/,
        loader: "babel-loader",
        include: [
          resolve("src"),
          resolve("node_modules/webpack-dev-server/client"),
        ],
        // exclude: [/static/,/iview-theme/],
        // options:{
        //     cacheDirectory: '.webpack_cache'
        // }
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        exclude: [resolve("src/icons/svg")],
        loader: "url-loader",
        options: {
          limit: 1024 * 10,
          name: utils.assetsPath("img/[name].[hash:7].[ext]"),
        },
      },
      {
        test: /\.svg$/,
        include: [resolve("src/icons/svg")],
        loader: "svg-sprite-loader",
        options: {
          symbolId: "icon-[name]", // 指定symbolId 不指定则默认为svg文件名
        },
      },
      {
        test: /\.webp$/i,
        use: [
          {
            loader: "file-loader",
          },
        ],
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        loader: "file-loader",
        options: {
          name: "[name].[hash:7].[ext]",
          outputPath: "media/",
        },
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        loader: "url-loader",
        options: {
          limit: 1024,
          name: utils.assetsPath("fonts/[name].[ext]"),
        },
      },
    ],
  },
  plugins: [new VueLoaderPlugin()],
  performance: {
    hints: false,
  },
};
